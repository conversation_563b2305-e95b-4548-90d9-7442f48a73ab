using System;
using System.Collections.Generic;
using System.Globalization;
using System.IO;
using System.Linq;
using System.Threading.Tasks;
using System.Transactions;
using Abp.Application.Services.Dto;
using Abp.Collections.Extensions;
using Abp.Domain.Entities;
using Abp.Domain.Repositories;
using Abp.Domain.Uow;
using Abp.EntityFrameworkCore;
using Abp.Extensions;
using Abp.Linq.Extensions;
using Abp.Runtime.Session;
using Abp.UI;
using aibase.AssayDatas;
using aibase.AssaySuites;
using aibase.Colours;
using aibase.Colours.Dto;
using aibase.ColourValues;
using aibase.DataEntries.Dto;
using aibase.DownholeDatas;
using aibase.DrillHoles.Services.CalculationDepthService;
using aibase.DrillHoles.Dto.CalculationDepth;
using Abp.EntityFrameworkCore.Repositories;
using aibase.DrillHoleEntity;
using aibase.DrillHoles.Services.CalculationDepthService.Handler;
using aibase.EntityFrameworkCore;
using aibase.GeologyDateValues;
using EFCore.BulkExtensions;
using aibase.GeologyDescriptionValues;
using aibase.GeologyFields;
using aibase.GeologySuiteFields;
using aibase.GeologySuites;
using aibase.GeologySuites.Services.GeologySuiteService;
using aibase.GeotechDatas;
using aibase.ImageCrops.Dto;
using aibase.ImageEntity;
using aibase.Images.Dto;
using aibase.Images.Services.UploadService.Handler;
using aibase.ImageSubtypes.Dto;
using aibase.ImageTypes.Dto;
using aibase.ImportDataServices.Dto;
using aibase.LoggingBars;
using aibase.LoggingViewColumns;
using aibase.LoggingViews;
using aibase.Models.Dto;
using aibase.Numbers.Dto;
using aibase.NumberValues;
using aibase.PickLists.Dto;
using aibase.PickListValues;
using aibase.RockGroupValues;
using aibase.RockLines;
using aibase.RockLines.Dto;
using aibase.RockNodeValues;
using aibase.RockSelectNumberValues;
using aibase.RockStyles.Dto;
using aibase.RockTree;
using aibase.RockTree.Dto;
using aibase.RockTypeNumberValues;
using aibase.RockTypes.Dto;
using aibase.Suites;
using AutoMapper;
using Microsoft.AspNetCore.Mvc;
using Microsoft.EntityFrameworkCore;
using Newtonsoft.Json;
using OfficeOpenXml;

namespace aibase.DataEntries.Services;

/// <inheritdoc />
public class DataEntryService : IDataEntryService
{
    private readonly IDbContextProvider<aibaseDbContext> _dbContextProvider;
    private readonly IRepository<DataEntry, int> _repository;
    private readonly IRepository<ColourValue, int> _colourValueRepository;
    private readonly IRepository<NumberValue, int> _numberValueRepository;
    private readonly IRepository<RockGroupValue, int> _rockGroupValueRepository;
    private readonly IRepository<PickListValue, int> _pickListValueRepository;
    private readonly IRepository<GeologyDescriptionValue, int> _geologyDescriptionValueRepository;
    private readonly IRepository<RockTypeNumberValue, int> _rockTypeNumberValueRepository;
    private readonly IRepository<RockSelectNumberValue, int> _rockSelectNumberValueRepository;
    private readonly IRepository<GeologyDateValue, int> _geologyDateValueRepository;
    private readonly IRepository<LoggingView, int> _loggingViewRepository;
    private readonly IRepository<LoggingViewColumn, int> _loggingViewColumnRepository;
    private readonly IRepository<AssayData, int> _assayDataRepository;
    private readonly IRepository<DownholeData, int> _geophysicsDataRepository;
    private readonly IRepository<Image, int> _imageRepository;
    private readonly IRepository<GeologySuiteField, int> _geologyFieldRepository;
    private readonly IRepository<RockNodeRelation, int> _rockNodeRelationRepository;
    private readonly IRepository<RockNode, int> _rockNodeRepository;
    private readonly IRepository<Colour, int> _colourRepository;
    private readonly IRepository<GeologySuite, int> _geologySuiteRepository;
    private readonly IRepository<AssaySuite, int> _assaySuiteRepository;
    private readonly IRepository<Suite, int> _suiteRepository;
    private readonly IRepository<GeotechData, int> _geotechDataRepository;
    private readonly IRepository<DrillHole, int> _drillholeRepository;
    private readonly IRepository<LoggingBar, int> _loggingBarRepository;
    private readonly IRockNodeValueRepository _rockNodeValueRepository;
    private readonly ICalculationDepthService _calculationDepthService;
    private readonly IRockNodeService _rockNodeService;
    private readonly IGeologySuiteService _geologySuiteService;
    private readonly IUnitOfWorkManager _unitOfWorkManager;
    private readonly IAbpSession _abpSession;
    private readonly IMapper _mapper;

    /// <summary>
    ///
    /// </summary>
    public DataEntryService(
        IDbContextProvider<aibaseDbContext> dbContextProvider,
        IRepository<DataEntry, int> repository,
        IRepository<ColourValue, int> colourValueRepository,
        IRepository<NumberValue, int> numberValueRepository,
        IRepository<RockGroupValue, int> rockGroupValueRepository,
        IRepository<PickListValue, int> pickListValueRepository,
        IRepository<GeologyDescriptionValue, int> geologyDescriptionValueRepository,
        IRepository<RockTypeNumberValue, int> rockTypeNumberValueRepository,
        IRepository<RockSelectNumberValue, int> rockSelectNumberValueRepository,
        IRepository<GeologyDateValue, int> geologyDateValueRepository,
        IRepository<LoggingView, int> loggingViewRepository,
        IRepository<LoggingViewColumn, int> loggingViewColumnRepository,
        IRepository<AssayData, int> assayDataRepository,
        IRepository<DownholeData, int> geophysicsDataRepository,
        IRepository<Image, int> imageRepository,
        IRepository<GeologySuiteField, int> geologyFieldRepository,
        IRepository<RockNodeRelation, int> rockNodeRelationRepository,
        IRepository<RockNode, int> rockNodeRepository,
        IRepository<Colour, int> colourRepository,
        IRepository<GeologySuite, int> geologySuiteRepository,
        IRepository<AssaySuite, int> assaySuiteRepository,
        IRepository<Suite, int> suiteRepository,
        IRepository<GeotechData, int> geotechDataRepository,
        IRepository<DrillHole, int> drillholeRepository,
        IRepository<LoggingBar, int> loggingBarRepository,
        IRockNodeValueRepository rockNodeValueRepository,
        ICalculationDepthService calculationDepthService,
        IRockNodeService rockNodeService,
        IGeologySuiteService geologySuiteService,
        IUnitOfWorkManager unitOfWorkManager,
        IMapper mapper,
        IAbpSession abpSession
    )
    {
        _dbContextProvider = dbContextProvider;
        _repository = repository;
        _colourValueRepository = colourValueRepository;
        _numberValueRepository = numberValueRepository;
        _rockGroupValueRepository = rockGroupValueRepository;
        _pickListValueRepository = pickListValueRepository;
        _geologyDescriptionValueRepository = geologyDescriptionValueRepository;
        _rockTypeNumberValueRepository = rockTypeNumberValueRepository;
        _rockSelectNumberValueRepository = rockSelectNumberValueRepository;
        _geologyDateValueRepository = geologyDateValueRepository;
        _loggingViewRepository = loggingViewRepository;
        _loggingViewColumnRepository = loggingViewColumnRepository;
        _geophysicsDataRepository = geophysicsDataRepository;
        _assayDataRepository = assayDataRepository;
        _imageRepository = imageRepository;
        _geologyFieldRepository = geologyFieldRepository;
        _rockNodeValueRepository = rockNodeValueRepository;
        _geologySuiteRepository = geologySuiteRepository;
        _assaySuiteRepository = assaySuiteRepository;
        _geologySuiteService = geologySuiteService;
        _colourRepository = colourRepository;
        _rockNodeService = rockNodeService;
        _rockNodeRelationRepository = rockNodeRelationRepository;
        _rockNodeRepository = rockNodeRepository;
        _loggingBarRepository = loggingBarRepository;
        _suiteRepository = suiteRepository;
        _geotechDataRepository = geotechDataRepository;
        _calculationDepthService = calculationDepthService;
        _drillholeRepository = drillholeRepository;
        _unitOfWorkManager = unitOfWorkManager;
        _abpSession = abpSession;
        _mapper = mapper;
    }

    /// <inheritdoc />
    public async Task<DataEntry> CreateDataEntryAsync(CreateDataEntryDto input)
    {
        using var transaction = _unitOfWorkManager.Begin(TransactionScopeOption.RequiresNew);
        try
        {
            double depthToMax = 0;
            if (input.DepthFrom == null)
            {
                var dataEntries = await _repository.GetAll()
                    .AsNoTracking()
                    .Where(x => x.DrillholeId == input.DrillholeId && x.GeologySuiteId == input.GeologySuiteId)
                    .Select(x => x.DepthTo)
                    .ToListAsync();
                depthToMax = dataEntries.Where(x => x.HasValue).Max() ?? 0;
            }

            var dataEntry = new DataEntry
            {
                GeologySuiteId = input.GeologySuiteId,
                DrillholeId = input.DrillholeId,
                DepthFrom = input.DepthFrom ?? depthToMax,
                DepthTo = input.DepthTo,
                ImageCropId = input.ImageCropId,
                X = input.X
            };
            await _repository.InsertAsync(dataEntry);
            await _unitOfWorkManager.Current.SaveChangesAsync();

            // Collect all values by type for batch processing
            var colourValues = new List<ColourValue>();
            var numberValues = new List<NumberValue>();
            var rockGroupValues = new List<RockGroupValue>();
            var pickListValues = new List<PickListValue>();
            var geologyDescriptionValues = new List<GeologyDescriptionValue>();
            var rockTypeNumberValues = new List<RockTypeNumberValue>();
            var rockSelectNumberValues = new List<RockSelectNumberValue>();
            var geologyDateValues = new List<GeologyDateValue>();
            var rockNodeValues = new List<RockNodeValue>();

            foreach (var dataEntryValue in input.DataEntryValues)
            {
                switch (dataEntryValue.FieldType)
                {
                    case FieldType.Colour:
                        colourValues.Add(new ColourValue
                        {
                            DataEntryId = dataEntry.Id,
                            GeologySuiteFieldId = dataEntryValue.GeologysuiteFieldId,
                            ColourId = dataEntryValue.ColourId,
                        });
                        break;

                    case FieldType.Number:
                        numberValues.Add(new NumberValue
                        {
                            DataEntryId = dataEntry.Id,
                            GeologySuiteFieldId = dataEntryValue.GeologysuiteFieldId,
                            Value = dataEntryValue.NumberValue
                        });
                        break;

                    case FieldType.RockGroup:
                        rockGroupValues.Add(new RockGroupValue
                        {
                            DataEntryId = dataEntry.Id,
                            GeologySuiteFieldId = dataEntryValue.GeologysuiteFieldId,
                            RockTypeId = dataEntryValue.RockTypeId,
                        });
                        break;

                    case FieldType.PickList:
                        pickListValues.Add(new PickListValue
                        {
                            DataEntryId = dataEntry.Id,
                            GeologySuiteFieldId = dataEntryValue.GeologysuiteFieldId,
                            PickListItemId = dataEntryValue.PickListItemId,
                        });
                        break;

                    case FieldType.Description:
                        geologyDescriptionValues.Add(new GeologyDescriptionValue
                        {
                            DataEntryId = dataEntry.Id,
                            GeologySuiteFieldId = dataEntryValue.GeologysuiteFieldId,
                            Description = dataEntryValue.Description
                        });
                        break;

                    case FieldType.RockTypeNumber:
                        rockTypeNumberValues.Add(new RockTypeNumberValue
                        {
                            DataEntryId = dataEntry.Id,
                            GeologySuiteFieldId = dataEntryValue.GeologysuiteFieldId,
                            NumberValue = dataEntryValue.NumberValue,
                        });
                        break;

                    case FieldType.RockSelectNumber:
                        rockSelectNumberValues.Add(new RockSelectNumberValue
                        {
                            DataEntryId = dataEntry.Id,
                            GeologySuiteFieldId = dataEntryValue.GeologysuiteFieldId,
                            RockTypeId = dataEntryValue.RockTypeId,
                            NumberValue = dataEntryValue.NumberValue
                        });
                        break;

                    case FieldType.Date:
                        geologyDateValues.Add(new GeologyDateValue
                        {
                            DataEntryId = dataEntry.Id,
                            GeologySuiteFieldId = dataEntryValue.GeologysuiteFieldId,
                            DateValue = dataEntryValue.DateValue,
                        });
                        break;

                    case FieldType.RockNode:
                        if (dataEntryValue.RockNodeId.HasValue)
                        {
                            // Validate RockNode using the new method
                            // The method itself throws if invalid, so no need to check a return value for validation status.
                            // The returned rockNode object can be used if needed, but for now, validation is key.
                            await _rockNodeService.GetAndValidateRockNodeForTenantAsync(dataEntryValue.RockNodeId.Value);

                            rockNodeValues.Add(new RockNodeValue
                            {
                                DataEntryId = dataEntry.Id,
                                GeologySuiteFieldId = dataEntryValue.GeologysuiteFieldId,
                                RockNodeId = dataEntryValue.RockNodeId.Value,
                            });
                        }
                        break;
                }
            }

            // Perform batch inserts using DbContext for better performance
            var dbContext = await _dbContextProvider.GetDbContextAsync();

            if (colourValues.Any())
                await dbContext.BulkInsertAsync(colourValues);
            if (numberValues.Any())
                await dbContext.BulkInsertAsync(numberValues);
            if (rockGroupValues.Any())
                await dbContext.BulkInsertAsync(rockGroupValues);
            if (pickListValues.Any())
                await dbContext.BulkInsertAsync(pickListValues);
            if (geologyDescriptionValues.Any())
                await dbContext.BulkInsertAsync(geologyDescriptionValues);
            if (rockTypeNumberValues.Any())
                await dbContext.BulkInsertAsync(rockTypeNumberValues);
            if (rockSelectNumberValues.Any())
                await dbContext.BulkInsertAsync(rockSelectNumberValues);
            if (geologyDateValues.Any())
                await dbContext.BulkInsertAsync(geologyDateValues);
            if (rockNodeValues.Any())
                await dbContext.BulkInsertAsync(rockNodeValues);

            await _unitOfWorkManager.Current.SaveChangesAsync();

            var calculateLoggingBar = new CalculateLoggingBarDto
            {
                GeologySuiteId = input.GeologySuiteId,
                DrillHoleId = input.DrillholeId,
                ImageSubtypeId = input.ImageCropId
            };
            await CalculateLoggingBarAsync(calculateLoggingBar);

            await transaction.CompleteAsync();
            return dataEntry;
        }
        catch (Exception ex)
        {
            throw new Exception("An error occurred while creating data entry", ex);
        }
    }

    /// <inheritdoc />
    public async Task<DataEntryResultDto> GetAllDataEntryAsync(PagedDataEntryRequestDto input)
    {
        var dataEntryIds = new List<int>();
        if (!input.Keyword.IsNullOrEmpty())
        {
            var ids = new List<int>();
            var colorValues = await _colourValueRepository.GetAll()
                .AsNoTracking()
                .Where(x => x.Colour != null && input.Keyword != null &&
                            x.Colour.Name.ToLower().Contains(input.Keyword.ToLower()))
                .Select(x => x.DataEntryId)
                .ToListAsync();
            if (colorValues.Count != 0) ids.AddRange(colorValues);

            var rockGroupValues = await _rockGroupValueRepository.GetAll()
                .AsNoTracking()
                .Where(x => x.RockType != null && input.Keyword != null &&
                            x.RockType.Name.ToLower().Contains(input.Keyword.ToLower()))
                .Select(x => x.DataEntryId)
                .ToListAsync();
            if (rockGroupValues.Count != 0) ids.AddRange(rockGroupValues);

            var pickListValues = await _pickListValueRepository.GetAll()
                .AsNoTracking()
                .Where(x => x.PickListItem != null && input.Keyword != null &&
                            x.PickListItem.Name.ToLower().Contains(input.Keyword.ToLower()))
                .Select(x => x.DataEntryId)
                .ToListAsync();
            if (pickListValues.Count != 0) ids.AddRange(pickListValues);

            var descriptionValues = await _geologyDescriptionValueRepository.GetAll()
                .AsNoTracking()
                .Where(x => x.Description != null && input.Keyword != null &&
                            x.Description.ToLower().Contains(input.Keyword.ToLower()))
                .Select(x => x.DataEntryId)
                .ToListAsync();
            if (descriptionValues.Count != 0) ids.AddRange(descriptionValues);

            var rockNodeValues = await _rockNodeValueRepository.GetAll()
                .AsNoTracking()
                .Where(x => input.Keyword != null && x.RockNode != null &&
                            x.RockNode.Name.ToLower().Contains(input.Keyword.ToLower()))
                .Select(x => x.DataEntryId)
                .ToListAsync();
            if (rockNodeValues.Count != 0) ids.AddRange(rockNodeValues);

            dataEntryIds.AddRange(ids.Select(x => x).Distinct());
        }

        var query = _repository.GetAllIncluding(x => x.DrillHole)
            .AsNoTracking()
            .Where(x => x.GeologySuiteId == input.GeologySuiteId)
            .WhereIf(input.DrillholeId != null, x => x.DrillholeId == input.DrillholeId)
            .WhereIf(!input.DrillHoleName.IsNullOrWhiteSpace(),
                x => input.DrillHoleName != null && x.DrillHole.Name.ToLower().Contains(input.DrillHoleName.ToLower()))
            .WhereIf(input.DepthFrom.HasValue, x => x.DepthFrom >= input.DepthFrom)
            .WhereIf(input.DepthTo.HasValue, x => x.DepthTo <= input.DepthTo)
            .WhereIf(dataEntryIds.Count > 0, x => dataEntryIds.Contains(x.Id));

        var sortDescending = input.SortOrder?.ToUpper() == "DESC";

        query = sortDescending
            ? query.OrderByDescending(r => !r.DepthFrom.HasValue || !r.DepthTo.HasValue)
                .ThenByDescending(r => r.DepthFrom)
                .ThenByDescending(r => r.DepthTo)
            : query.OrderBy(r => !r.DepthFrom.HasValue || !r.DepthTo.HasValue)
                .ThenBy(r => r.DepthFrom)
                .ThenBy(r => r.DepthTo);

        var totalCount = query.Count();

        var skipCount = Math.Max(0, totalCount - input.SkipCount - input.MaxResultCount);

        var dataEntries = await query
            .Skip(skipCount)
            .Take(input.MaxResultCount)
            .ToListAsync();

        if (dataEntries.Count == 0)
            return new DataEntryResultDto
            {
                TotalCount = totalCount,
                Items = [],
            };

        var dataEntryDto = _mapper.Map<List<DataEntryDto>>(dataEntries);

        // // Apply reverse calculation for backward compatibility
        // await ApplyReverseCalculationForBackwardCompatibility(dataEntryDto);

        var geologySuiteFields = await _geologyFieldRepository.GetAll()
            .AsNoTracking()
            .Include(x => x.GeologyField)
            .ThenInclude(x => x.RockNode)
            .Where(x => x.GeologySuiteId == input.GeologySuiteId)
            .Select(x => new DataEntryValueDto
            {
                GeologysuiteFieldId = x.Id,
                Sequence = x.Sequence,
                FieldName = x.Name,
                FieldType = x.GeologyField.Type,
                // RockNode = _mapper.Map<RockNodeDto>(x.GeologyField.RockNode),
                // RockNodeId = x.GeologyField.RockNodeId
            })
            .ToListAsync();

        await HandleGetDataEntryAsync(geologySuiteFields, dataEntryDto);

        return new DataEntryResultDto
        {
            TotalCount = totalCount,
            Items = dataEntryDto,
        };
    }

    /// <inheritdoc />
    public async Task HandleGetDataEntryAsync(List<DataEntryValueDto> geologySuiteFields,
        List<DataEntryDto> dataEntryDto)
    {
        foreach (var geologySuiteField in geologySuiteFields)
        {
            if (geologySuiteField is not { FieldType: FieldType.RockNode, RockNode: not null }) continue;
            var treeNode = new TreeNodeDto
            {
                Id = geologySuiteField.RockNode.Id,
                Name = geologySuiteField.RockNode.Name,
                Code = geologySuiteField.RockNode.Code,
                Description = geologySuiteField.RockNode.Description,
                NodeType = geologySuiteField.RockNode.NodeType,
                RockTypeId = geologySuiteField.RockNode.RockTypeId,
                IsActive = geologySuiteField.RockNode.IsActive,
                DisplayColor = geologySuiteField.RockNode.DisplayColor,
                IconUrl = geologySuiteField.RockNode.IconUrl,
                ParentId = null // Root nodes have no parent
            };

            await BuildTreeRecursivelyAsync(treeNode, _abpSession.GetTenantId(), []);

            geologySuiteField.TreeNode = treeNode;
        }

        var allValues =
            await FetchAllDataEntryValuesAsync(dataEntryDto.Select(d => d.Id).ToList());

        foreach (var dataEntry in dataEntryDto)
        {
            var dataEntryValues = geologySuiteFields
                .Select(f => new DataEntryValueDto
                {
                    GeologysuiteFieldId = f.GeologysuiteFieldId,
                    Sequence = f.Sequence,
                    FieldName = f.FieldName,
                    FieldType = f.FieldType,
                    RockNodeId = f.RockNodeId,
                    RockNode = f.RockNode,
                    TreeNode = f.TreeNode
                })
                .ToList();

            var entrySpecificValues = allValues
                .Where(v => v.DataEntryId == dataEntry.Id)
                .ToList();

            dataEntryValues.AddRange(entrySpecificValues);

            dataEntry.DataEntryValues = dataEntryValues
                .GroupBy(v => v.GeologysuiteFieldId)
                .Select(group =>
                {
                    if (group.Any(v => v.FieldType == FieldType.RockNode))
                    {
                        var treeNodeSource = group.FirstOrDefault(v => v.TreeNode != null);
                        if (treeNodeSource != null)
                        {
                            foreach (var item in group.Where(v => v.TreeNode == null))
                            {
                                item.TreeNode = treeNodeSource.TreeNode;
                            }
                        }
                    }

                    var validValue = group.FirstOrDefault(v => v.ValueId != 0);
                    return validValue ?? group.First();
                })
                .OrderBy(v => v.Sequence)
                .ThenBy(v => v.FieldName)
                .ToList();
        }
    }

    /// <inheritdoc />
    public async Task<IActionResult> GetAllDataEntryByDrillholeAsync(PagedDataEntryByDrillholeRequestDto input)
    {
        List<int> drillHoleIds;
        try
        {
            drillHoleIds = JsonConvert.DeserializeObject<List<int>>(input.DrillholeIds) ?? [];
        }
        catch
        {
            drillHoleIds = [];
        }

        List<int> geologySuiteFieldIds;
        try
        {
            geologySuiteFieldIds = JsonConvert.DeserializeObject<List<int>>(input.GeologySuiteFieldIds) ?? [];
        }
        catch
        {
            geologySuiteFieldIds = [];
        }

        var query = _repository.GetAll()
            .AsNoTracking()
            .WhereIf(drillHoleIds.Count > 0, x => drillHoleIds.Contains(x.DrillholeId))
            .WhereIf(input.GeologySuiteId.HasValue, x => x.GeologySuiteId == input.GeologySuiteId)
            .WhereIf(input.DepthFrom.HasValue, x => x.DepthFrom >= input.DepthFrom)
            .WhereIf(input.DepthTo.HasValue, x => x.DepthTo <= input.DepthTo);

        var sortDescending = input.SortOrder?.ToUpper() == "DESC";

        query = sortDescending
            ? query.OrderByDescending(r => !r.DepthFrom.HasValue || !r.DepthTo.HasValue)
                .ThenByDescending(r => r.DepthFrom)
                .ThenByDescending(r => r.DepthTo)
            : query.OrderBy(r => !r.DepthFrom.HasValue || !r.DepthTo.HasValue)
                .ThenBy(r => r.DepthFrom)
                .ThenBy(r => r.DepthTo);

        var dataEntries = await query
            .ToListAsync();

        var dataEntryDto = _mapper.Map<List<DataEntryDto>>(dataEntries);

        foreach (var dataEntry in dataEntryDto)
        {
            var colorValues = await _colourValueRepository.GetAllIncluding(x => x.Colour, x => x.GeologySuiteField)
                .AsNoTracking()
                .Where(x => x.DataEntryId == dataEntry.Id && x.GeologySuiteField.IsActive)
                .WhereIf(geologySuiteFieldIds.Count > 0, x => geologySuiteFieldIds.Contains(x.GeologySuiteFieldId))
                .Select(x => new DataEntryValueDto
                {
                    ValueId = x.Id,
                    GeologysuiteFieldId = x.GeologySuiteFieldId,
                    Sequence = x.GeologySuiteField.Sequence,
                    FieldName = x.GeologySuiteField.Name,
                    FieldType = FieldType.Colour,
                    ColourId = x.ColourId,
                    Colour = x.Colour != null
                        ? new ColourDto
                        {
                            Id = x.Colour.Id,
                            Name = x.Colour.Name,
                            HexCode = x.Colour.HexCode,
                            IsActive = x.Colour.IsActive,
                        }
                        : null
                })
                .ToListAsync();
            if (colorValues.Count > 0)
            {
                dataEntry.DataEntryValues.AddRange(colorValues);
            }

            var numberValues = await _numberValueRepository.GetAll()
                .AsNoTracking()
                .Include(x => x.GeologySuiteField)
                .ThenInclude(x => x.GeologyField)
                .ThenInclude(x => x.Number)
                .ThenInclude(x => x.Unit)
                .Where(x => x.DataEntryId == dataEntry.Id && x.GeologySuiteField.IsActive)
                .WhereIf(geologySuiteFieldIds.Count > 0, x => geologySuiteFieldIds.Contains(x.GeologySuiteFieldId))
                .Select(x => new DataEntryValueDto
                {
                    ValueId = x.Id,
                    GeologysuiteFieldId = x.GeologySuiteFieldId,
                    Sequence = x.GeologySuiteField.Sequence,
                    FieldName = x.GeologySuiteField.Name,
                    FieldType = FieldType.Number,
                    NumberValue = x.Value,
                    Number = new NumberDto()
                    {
                        Name = x.GeologySuiteField.GeologyField.Number.Name,
                        ValueType = x.GeologySuiteField.GeologyField.Number.ValueType,
                        UpperLimit = x.GeologySuiteField.GeologyField.Number.UpperLimit,
                        LowerLimit = x.GeologySuiteField.GeologyField.Number.LowerLimit,
                        IsPercent = x.GeologySuiteField.GeologyField.Number.IsPercent,
                        IsActive = x.GeologySuiteField.GeologyField.Number.IsActive,
                        Unit = x.GeologySuiteField.GeologyField.Number.Unit,
                    }
                })
                .ToListAsync();
            if (numberValues.Count > 0)
            {
                dataEntry.DataEntryValues.AddRange(numberValues);
            }

            // Get rock group values
            var rockGroupValues = await _rockGroupValueRepository
                .GetAllIncluding(x => x.RockType, x => x.GeologySuiteField)
                .AsNoTracking()
                .Where(x => x.DataEntryId == dataEntry.Id && x.GeologySuiteField.IsActive)
                .WhereIf(geologySuiteFieldIds.Count > 0, x => geologySuiteFieldIds.Contains(x.GeologySuiteFieldId))
                .Select(x => new DataEntryValueDto
                {
                    ValueId = x.Id,
                    GeologysuiteFieldId = x.GeologySuiteFieldId,
                    Sequence = x.GeologySuiteField.Sequence,
                    FieldName = x.GeologySuiteField.Name,
                    FieldType = FieldType.RockGroup,
                    RockTypeId = x.RockTypeId,
                    RockType = x.RockType != null
                        ? new RockTypeDto
                        {
                            Id = x.RockType.Id,
                            Name = x.RockType.Name,
                            Code = x.RockType.Code,
                            Description = x.RockType.Description,
                            IsActive = x.RockType.IsActive,
                            RockStyle = _mapper.Map<RockStyleDto>(x.RockType.RockStyle)
                        }
                        : null
                })
                .ToListAsync();
            if (rockGroupValues.Count > 0)
            {
                dataEntry.DataEntryValues.AddRange(rockGroupValues);
            }

            // Get picklist values
            var pickListValues = await _pickListValueRepository
                .GetAllIncluding(x => x.PickListItem, x => x.GeologySuiteField)
                .AsNoTracking()
                .Where(x => x.DataEntryId == dataEntry.Id && x.GeologySuiteField.IsActive)
                .WhereIf(geologySuiteFieldIds.Count > 0, x => geologySuiteFieldIds.Contains(x.GeologySuiteFieldId))
                .Select(x => new DataEntryValueDto
                {
                    ValueId = x.Id,
                    GeologysuiteFieldId = x.GeologySuiteFieldId,
                    Sequence = x.GeologySuiteField.Sequence,
                    FieldName = x.GeologySuiteField.Name,
                    FieldType = FieldType.PickList,
                    PickListItemId = x.PickListItemId,
                    PickListItem = x.PickListItem != null
                        ? new PickListItemDto
                        {
                            Id = x.PickListItem.Id,
                            Name = x.PickListItem.Name,
                            Sequence = x.PickListItem.Sequence,
                            IsActive = x.PickListItem.IsActive,
                        }
                        : null
                })
                .ToListAsync();
            if (pickListValues.Count > 0)
            {
                dataEntry.DataEntryValues.AddRange(pickListValues);
            }

            // Get description values
            var descriptionValues = await _geologyDescriptionValueRepository.GetAllIncluding(x => x.GeologySuiteField)
                .AsNoTracking()
                .Where(x => x.DataEntryId == dataEntry.Id && x.GeologySuiteField.IsActive)
                .WhereIf(geologySuiteFieldIds.Count > 0, x => geologySuiteFieldIds.Contains(x.GeologySuiteFieldId))
                .Select(x => new DataEntryValueDto
                {
                    ValueId = x.Id,
                    GeologysuiteFieldId = x.GeologySuiteFieldId,
                    Sequence = x.GeologySuiteField.Sequence,
                    FieldName = x.GeologySuiteField.Name,
                    FieldType = FieldType.Description,
                    Description = x.Description
                })
                .ToListAsync();
            if (descriptionValues.Count > 0)
            {
                dataEntry.DataEntryValues.AddRange(descriptionValues);
            }

            // Get rock type number values
            var rockTypeNumberValues = await _rockTypeNumberValueRepository.GetAll()
                .AsNoTracking()
                .Include(x => x.GeologySuiteField)
                .ThenInclude(x => x.GeologyField)
                .ThenInclude(x => x.RockTypeNumber)
                .ThenInclude(x => x.RockType)
                .ThenInclude(x => x.RockStyle)
                .Include(x => x.GeologySuiteField)
                .ThenInclude(x => x.GeologyField)
                .ThenInclude(x => x.RockTypeNumber)
                .ThenInclude(x => x.Number)
                .ThenInclude(x => x.Unit)
                .Where(x => x.DataEntryId == dataEntry.Id && x.GeologySuiteField.IsActive)
                .WhereIf(geologySuiteFieldIds.Count > 0, x => geologySuiteFieldIds.Contains(x.GeologySuiteFieldId))
                .ToListAsync();

            var rockTypeNumberResult = rockTypeNumberValues.Select(x => new DataEntryValueDto
            {
                ValueId = x.Id,
                GeologysuiteFieldId = x.GeologySuiteFieldId,
                Sequence = x.GeologySuiteField.Sequence,
                FieldName = x.GeologySuiteField.Name,
                FieldType = FieldType.RockTypeNumber,
                NumberValue = x.NumberValue,
                Number = _mapper.Map<NumberDto>(x.GeologySuiteField.GeologyField.RockTypeNumber?.Number),
                RockType = _mapper.Map<RockTypeDto>(x.GeologySuiteField.GeologyField.RockTypeNumber?.RockType)
            }).ToList();
            if (rockTypeNumberResult.Count > 0)
            {
                dataEntry.DataEntryValues.AddRange(rockTypeNumberResult);
            }

            // Get rock select number values
            var rockSelectNumberValues = await _rockSelectNumberValueRepository.GetAllIncluding(x => x.RockType)
                .AsNoTracking()
                .Include(x => x.GeologySuiteField)
                .ThenInclude(x => x.GeologyField)
                .ThenInclude(x => x.RockSelectNumber)
                .ThenInclude(x => x.Number)
                .ThenInclude(x => x.Unit)
                .Where(x => x.DataEntryId == dataEntry.Id && x.GeologySuiteField.IsActive)
                .WhereIf(geologySuiteFieldIds.Count > 0, x => geologySuiteFieldIds.Contains(x.GeologySuiteFieldId))
                .Select(x => new DataEntryValueDto
                {
                    ValueId = x.Id,
                    GeologysuiteFieldId = x.GeologySuiteFieldId,
                    Sequence = x.GeologySuiteField.Sequence,
                    FieldName = x.GeologySuiteField.Name,
                    FieldType = FieldType.RockSelectNumber,
                    RockTypeId = x.RockTypeId,
                    RockType = x.RockType != null
                        ? new RockTypeDto
                        {
                            Id = x.RockType.Id,
                            Name = x.RockType.Name,
                            Code = x.RockType.Code,
                            Description = x.RockType.Description,
                            IsActive = x.RockType.IsActive,
                            RockStyle = _mapper.Map<RockStyleDto>(x.RockType.RockStyle)
                        }
                        : null,
                    NumberValue = x.NumberValue,
                    Number = x.GeologySuiteField.GeologyField.RockSelectNumber != null
                        ? _mapper.Map<NumberDto>(x.GeologySuiteField.GeologyField.RockSelectNumber.Number)
                        : null,
                })
                .ToListAsync();
            if (rockSelectNumberValues.Count > 0)
            {
                dataEntry.DataEntryValues.AddRange(rockSelectNumberValues);
            }

            // Get date values
            var dateValues = await _geologyDateValueRepository.GetAllIncluding(x => x.GeologySuiteField)
                .AsNoTracking()
                .Where(x => x.DataEntryId == dataEntry.Id && x.GeologySuiteField.IsActive)
                .WhereIf(geologySuiteFieldIds.Count > 0, x => geologySuiteFieldIds.Contains(x.GeologySuiteFieldId))
                .Select(x => new DataEntryValueDto
                {
                    ValueId = x.Id,
                    GeologysuiteFieldId = x.GeologySuiteFieldId,
                    Sequence = x.GeologySuiteField.Sequence,
                    FieldName = x.GeologySuiteField.Name,
                    FieldType = FieldType.Date,
                    DateValue = x.DateValue
                })
                .ToListAsync();
            if (dateValues.Count > 0)
            {
                dataEntry.DataEntryValues.AddRange(dateValues);
            }

            // Get RockNode values
            var rockNodeValues = await _rockNodeValueRepository.GetAll()
                .AsNoTracking()
                .Include(x => x.GeologySuiteField)
                .Where(x => x.DataEntryId == dataEntry.Id && x.GeologySuiteField.IsActive)
                .WhereIf(geologySuiteFieldIds.Count > 0, x => geologySuiteFieldIds.Contains(x.GeologySuiteFieldId))
                .Select(x => new DataEntryValueDto
                {
                    ValueId = x.Id,
                    GeologysuiteFieldId = x.GeologySuiteFieldId,
                    Sequence = x.GeologySuiteField.Sequence,
                    FieldName = x.GeologySuiteField.Name,
                    FieldType = FieldType.RockNode,
                    RockNodeId = x.RockNodeId
                })
                .ToListAsync();
            if (rockNodeValues.Count > 0)
            {
                dataEntry.DataEntryValues.AddRange(rockNodeValues);
            }

            dataEntry.DataEntryValues = dataEntry.DataEntryValues.OrderBy(x => x.Sequence).ToList();
        }

        var groupedData = dataEntryDto
            .GroupBy(x => x.DrillholeId)
            .Select(g => new
            {
                DrillholeId = g.Key,
                DataEntry = g.ToList()
            })
            .ToList();

        var response = new
        {
            TotalCount = groupedData.Count,
            items = groupedData,
        };

        return new OkObjectResult(response);
    }

    /// <inheritdoc />
    public async Task<IActionResult> GetDataEntryByLoggingViewAsync(PagedDataEntryByLoggingViewRequestDto input)
    {
        var loggingView = await _loggingViewRepository.FirstOrDefaultAsync(x => x.Id == input.LoggingViewId);
        if (loggingView == null)
        {
            throw new EntityNotFoundException(typeof(LoggingView), input.LoggingViewId);
        }

        var loggingViewColumns = await _loggingViewColumnRepository
            .GetAllIncluding(x => x.AssayAttribute, x => x.Attribute)
            .Include(x => x.ImageType)
            .Include(x => x.ImageSubtype)
            .AsNoTracking()
            .Where(x => x.LoggingViewId == input.LoggingViewId)
            .ToListAsync();

        var loggingViewColumnCoreRows = loggingViewColumns.Where(x => x.ColumnClass == ColumnClass.CoreRow).ToList();

        // Get data entry Geology
        List<DataEntryGeologySuiteDto> dataEntryGeologySuitesDto = [];
        var geologySuiteIds = loggingViewColumns.Select(x => x.GeologySuiteId).ToList();
        var geologySuiteFieldIds = loggingViewColumns.Select(x => x.GeologySuiteFieldId).ToList();

        var query = _repository.GetAll()
            .AsNoTracking()
            .Where(x => x.DrillholeId == input.DrillHoleId && geologySuiteIds.Contains(x.GeologySuiteId))
            .OrderByDescending(r => r.DepthFrom);

        var totalCount = await query.CountAsync();
        var dataEntries = await query.ToListAsync();
        var dataEntryDto = _mapper.Map<List<DataEntryDto>>(dataEntries);

        var dataEntryIds = dataEntryDto.Select(d => d.Id).ToList();

        // color
        var colorValues = await _colourValueRepository.GetAllIncluding(x => x.Colour, x => x.GeologySuiteField)
            .AsNoTracking()
            .Where(x => dataEntryIds.Contains(x.DataEntryId)
                        && x.GeologySuiteField.IsActive
                        && geologySuiteFieldIds.Contains(x.GeologySuiteFieldId))
            .Select(x => new DataEntryValueDto
            {
                ValueId = x.Id,
                DataEntryId = x.DataEntryId,
                GeologysuiteFieldId = x.GeologySuiteFieldId,
                Sequence = x.GeologySuiteField.Sequence,
                FieldName = x.GeologySuiteField.Name,
                FieldType = FieldType.Colour,
                ColourId = x.ColourId,
                Colour = x.Colour != null ? _mapper.Map<ColourDto>(x.Colour) : null
            })
            .ToListAsync();
        AssignValuesToDataEntries(dataEntryDto, colorValues);

        // number
        var numberValues = await _numberValueRepository.GetAll()
            .AsNoTracking()
            .Include(x => x.GeologySuiteField)
            .ThenInclude(x => x.GeologyField)
            .ThenInclude(x => x.Number)
            .ThenInclude(x => x.Unit)
            .Where(x => dataEntryIds.Contains(x.DataEntryId)
                        && x.GeologySuiteField.IsActive
                        && geologySuiteFieldIds.Contains(x.GeologySuiteFieldId))
            .Select(x => new DataEntryValueDto
            {
                ValueId = x.Id,
                DataEntryId = x.DataEntryId,
                GeologysuiteFieldId = x.GeologySuiteFieldId,
                Sequence = x.GeologySuiteField.Sequence,
                FieldName = x.GeologySuiteField.Name,
                FieldType = FieldType.Number,
                NumberValue = x.Value,
                Number = new NumberDto
                {
                    Name = x.GeologySuiteField.GeologyField.Number.Name,
                    ValueType = x.GeologySuiteField.GeologyField.Number.ValueType,
                    UpperLimit = x.GeologySuiteField.GeologyField.Number.UpperLimit,
                    LowerLimit = x.GeologySuiteField.GeologyField.Number.LowerLimit,
                    IsPercent = x.GeologySuiteField.GeologyField.Number.IsPercent,
                    IsActive = x.GeologySuiteField.GeologyField.Number.IsActive,
                    Unit = x.GeologySuiteField.GeologyField.Number.Unit,
                }
            })
            .ToListAsync();
        AssignValuesToDataEntries(dataEntryDto, numberValues);

        // rock group
        var rockGroupValues = await _rockGroupValueRepository
            .GetAllIncluding(x => x.GeologySuiteField)
            .Include(x => x.RockType)
            .ThenInclude(x => x.RockStyle)
            .AsNoTracking()
            .Where(x => dataEntryIds.Contains(x.DataEntryId)
                        && x.GeologySuiteField.IsActive
                        && geologySuiteFieldIds.Contains(x.GeologySuiteFieldId))
            .Select(x => new DataEntryValueDto
            {
                ValueId = x.Id,
                DataEntryId = x.DataEntryId,
                GeologysuiteFieldId = x.GeologySuiteFieldId,
                Sequence = x.GeologySuiteField.Sequence,
                FieldName = x.GeologySuiteField.Name,
                FieldType = FieldType.RockGroup,
                RockTypeId = x.RockTypeId, RockType = x.RockType != null
                    ? new RockTypeDto
                    {
                        Id = x.RockType.Id,
                        Name = x.RockType.Name,
                        Code = x.RockType.Code,
                        Description = x.RockType.Description,
                        IsActive = x.RockType.IsActive,
                        RockStyle = _mapper.Map<RockStyleDto>(x.RockType.RockStyle)
                    }
                    : null
            })
            .ToListAsync();
        AssignValuesToDataEntries(dataEntryDto, rockGroupValues);

        // picklist
        var pickListValues = await _pickListValueRepository
            .GetAllIncluding(x => x.PickListItem, x => x.GeologySuiteField)
            .AsNoTracking()
            .Where(x => dataEntryIds.Contains(x.DataEntryId) && x.GeologySuiteField.IsActive &&
                        geologySuiteFieldIds.Contains(x.GeologySuiteFieldId))
            .Select(x => new DataEntryValueDto
            {
                ValueId = x.Id,
                DataEntryId = x.DataEntryId,
                GeologysuiteFieldId = x.GeologySuiteFieldId,
                Sequence = x.GeologySuiteField.Sequence,
                FieldName = x.GeologySuiteField.Name,
                FieldType = FieldType.PickList,
                PickListItemId = x.PickListItemId,
                PickListItem = x.PickListItem != null
                    ? new PickListItemDto
                    {
                        Id = x.PickListItem.Id,
                        Name = x.PickListItem.Name,
                        Sequence = x.PickListItem.Sequence,
                        IsActive = x.PickListItem.IsActive,
                    }
                    : null
            })
            .ToListAsync();
        AssignValuesToDataEntries(dataEntryDto, pickListValues);

        // description
        var descriptionValues = await _geologyDescriptionValueRepository
            .GetAllIncluding(x => x.GeologySuiteField)
            .AsNoTracking()
            .Where(x => dataEntryIds.Contains(x.DataEntryId) && x.GeologySuiteField.IsActive &&
                        geologySuiteFieldIds.Contains(x.GeologySuiteFieldId))
            .Select(x => new DataEntryValueDto
            {
                ValueId = x.Id,
                DataEntryId = x.DataEntryId,
                GeologysuiteFieldId = x.GeologySuiteFieldId,
                Sequence = x.GeologySuiteField.Sequence,
                FieldName = x.GeologySuiteField.Name,
                FieldType = FieldType.Description,
                Description = x.Description
            })
            .ToListAsync();
        AssignValuesToDataEntries(dataEntryDto, descriptionValues);

        // rock type number
        var rockTypeNumberValues = await _rockTypeNumberValueRepository.GetAll()
            .AsNoTracking()
            .Include(x => x.GeologySuiteField)
            .ThenInclude(x => x.GeologyField)
            .ThenInclude(x => x.RockTypeNumber)
            .ThenInclude(x => x.RockType)
            .ThenInclude(x => x.RockStyle)
            .Include(x => x.GeologySuiteField)
            .ThenInclude(x => x.GeologyField)
            .ThenInclude(x => x.RockTypeNumber)
            .ThenInclude(x => x.Number)
            .ThenInclude(x => x.Unit)
            .Where(x => dataEntryIds.Contains(x.DataEntryId) && x.GeologySuiteField.IsActive &&
                        geologySuiteFieldIds.Contains(x.GeologySuiteFieldId))
            .ToListAsync();
        var rockTypeNumberResult = rockTypeNumberValues.Select(x => new DataEntryValueDto
        {
            ValueId = x.Id,
            DataEntryId = x.DataEntryId,
            GeologysuiteFieldId = x.GeologySuiteFieldId,
            Sequence = x.GeologySuiteField.Sequence,
            FieldName = x.GeologySuiteField.Name,
            FieldType = FieldType.RockTypeNumber,
            NumberValue = x.NumberValue,
            Number = _mapper.Map<NumberDto>(x.GeologySuiteField.GeologyField.RockTypeNumber?.Number),
            RockType = _mapper.Map<RockTypeDto>(x.GeologySuiteField.GeologyField.RockTypeNumber
                ?.RockType)
        }).ToList();
        AssignValuesToDataEntries(dataEntryDto, rockTypeNumberResult);

        // rock select number
        var rockSelectNumberValues = await _rockSelectNumberValueRepository
            .GetAllIncluding(x => x.RockType)
            .AsNoTracking()
            .Include(x => x.GeologySuiteField)
            .ThenInclude(x => x.GeologyField)
            .ThenInclude(x => x.RockSelectNumber)
            .ThenInclude(x => x.Number)
            .ThenInclude(x => x.Unit)
            .Where(x => dataEntryIds.Contains(x.DataEntryId) && x.GeologySuiteField.IsActive &&
                        geologySuiteFieldIds.Contains(x.GeologySuiteFieldId))
            .Select(x => new DataEntryValueDto
            {
                ValueId = x.Id,
                DataEntryId = x.DataEntryId,
                GeologysuiteFieldId = x.GeologySuiteFieldId,
                Sequence = x.GeologySuiteField.Sequence,
                FieldName = x.GeologySuiteField.Name,
                FieldType = FieldType.RockSelectNumber,
                RockTypeId = x.RockTypeId,
                RockType = x.RockType != null
                    ? new RockTypeDto
                    {
                        Id = x.RockType.Id,
                        Name = x.RockType.Name,
                        Code = x.RockType.Code,
                        Description = x.RockType.Description,
                        IsActive = x.RockType.IsActive,
                        RockStyle = _mapper.Map<RockStyleDto>(x.RockType.RockStyle)
                    }
                    : null,
                NumberValue = x.NumberValue,
                Number = x.GeologySuiteField.GeologyField.RockSelectNumber != null
                    ? _mapper.Map<NumberDto>(
                        x.GeologySuiteField.GeologyField.RockSelectNumber.Number)
                    : null,
            })
            .ToListAsync();
        AssignValuesToDataEntries(dataEntryDto, rockSelectNumberValues);

        // date
        var dateValues = await _geologyDateValueRepository.GetAllIncluding(x => x.GeologySuiteField)
            .AsNoTracking()
            .Where(x => dataEntryIds.Contains(x.DataEntryId) && x.GeologySuiteField.IsActive &&
                        geologySuiteFieldIds.Contains(x.GeologySuiteFieldId))
            .Select(x => new DataEntryValueDto
            {
                ValueId = x.Id,
                DataEntryId = x.DataEntryId,
                GeologysuiteFieldId = x.GeologySuiteFieldId,
                Sequence = x.GeologySuiteField.Sequence,
                FieldName = x.GeologySuiteField.Name,
                FieldType = FieldType.Date,
                DateValue = x.DateValue
            })
            .ToListAsync();
        AssignValuesToDataEntries(dataEntryDto, dateValues);

        // rock node
        var rockNodeValues = await _rockNodeValueRepository.GetAll()
            .AsNoTracking()
            .Include(x => x.GeologySuiteField)
            .Include(x => x.RockNode)
            .ThenInclude(x => x.RockType)
            .Where(x => dataEntryIds.Contains(x.DataEntryId) && x.GeologySuiteField.IsActive &&
                        geologySuiteFieldIds.Contains(x.GeologySuiteFieldId))
            .Select(x => new DataEntryValueDto
            {
                ValueId = x.Id,
                DataEntryId = x.DataEntryId,
                GeologysuiteFieldId = x.GeologySuiteFieldId,
                Sequence = x.GeologySuiteField.Sequence,
                FieldName = x.GeologySuiteField.Name,
                FieldType = FieldType.RockNode,
                RockNodeId = x.RockNodeId,
                RockNode = x.RockNode != null
                    ? new RockNodeDto
                    {
                        Id = x.RockNode.Id,
                        Name = x.RockNode.Name,
                        Code = x.RockNode.Code,
                        Description = x.RockNode.Description,
                        NodeType = x.RockNode.NodeType,
                        IsActive = x.RockNode.IsActive,
                        DisplayColor = x.RockNode.DisplayColor,
                        IconUrl = x.RockNode.IconUrl,
                        RockType = _mapper.Map<RockTypeDto>(x.RockNode.RockType)
                    }
                    : null
            })
            .ToListAsync();
        AssignValuesToDataEntries(dataEntryDto, rockNodeValues);

        foreach (var dataEntry in dataEntryDto)
        {
            dataEntry.DataEntryValues = dataEntry.DataEntryValues.OrderBy(x => x.Sequence).ToList();
        }

        var result = new DataEntryGeologySuiteDto
        {
            TotalCount = totalCount,
            DataEntries = dataEntryDto
        };
        dataEntryGeologySuitesDto.Add(result);

        // Get data entry Assay
        List<Dictionary<string, string>> assayData = [];
        var attributeAssayNames = loggingViewColumns
            .Where(x => !string.IsNullOrEmpty(x.AssayAttribute?.Name))
            .Select(x => x.AssayAttribute?.Name)
            .ToList();
        var assaySuiteIds = loggingViewColumns
            .Where(x => x.AssaySuiteId != null)
            .Select(x => x.AssaySuiteId)
            .Distinct()
            .ToList();
        if (assaySuiteIds.Count > 0 && attributeAssayNames.Count > 0)
        {
            var rawQuery = _assayDataRepository.GetAll()
                .AsNoTracking()
                .Where(x => x.DrillHoleId == input.DrillHoleId && assaySuiteIds.Contains(x.AssaySuiteId) &&
                            (attributeAssayNames.Contains(x.AttributeName) ||
                             x.AttributeName.ToLower().Contains("depth")))
                .Select(x => new
                {
                    x.AttributeName,
                    x.AttributeValue,
                    x.GroupId,
                    x.DrillHole
                });

            var dataRaw = await rawQuery.ToListAsync();

            assayData = dataRaw
                .GroupBy(d => d.GroupId)
                .Select(g =>
                {
                    var dict = g.ToDictionary(
                        d => d.AttributeName,
                        d => d.AttributeValue);
                    dict["groupId"] = g.Key;
                    dict["DrillHole"] = g.First().DrillHole ?? string.Empty;
                    return dict;
                })
                .ToList();
        }

        // Get data entry Geophysics
        List<Dictionary<string, string>> geophysicsData = [];
        var attributeGeophysicsNames = loggingViewColumns
            .Where(x => !string.IsNullOrEmpty(x.Attribute?.Name))
            .Select(x => x.Attribute?.Name)
            .ToList();
        var geophysicsSuiteIds = loggingViewColumns
            .Where(x => x.SuiteId != null)
            .Select(x => x.SuiteId)
            .ToList();
        if (geophysicsSuiteIds.Count > 0 && attributeGeophysicsNames.Count > 0)
        {
            var rawQuery = _geophysicsDataRepository.GetAll()
                .AsNoTracking()
                .Where(x => x.DrillHoleId == input.DrillHoleId && geophysicsSuiteIds.Contains(x.SuiteId) &&
                            (attributeGeophysicsNames.Contains(x.AttributeName) ||
                             x.AttributeName.ToLower().Contains("depth")))
                .Select(x => new
                {
                    x.AttributeName,
                    x.AttributeValue,
                    x.GroupId,
                    x.DrillHole
                });

            var dataRaw = await rawQuery.ToListAsync();

            geophysicsData = dataRaw
                .GroupBy(d => d.GroupId)
                .Select(g =>
                {
                    var dict = g.ToDictionary(
                        d => d.AttributeName,
                        d => d.AttributeValue);
                    dict["groupId"] = g.Key;
                    dict["DrillHole"] = g.First().DrillHole ?? string.Empty;
                    return dict;
                })
                .ToList();
        }

        //GET standard Image crop by Drillhole
        var images = await _imageRepository
            .GetAllIncluding(x => x.CroppedImages)
            .AsNoTracking()
            .Where(x => x.DrillHoleId == input.DrillHoleId)
            .ToListAsync();

        var coreRows = new List<CoreRowDto>();
        foreach (var loggingViewColumnCoreRow in loggingViewColumnCoreRows)
        {
            var imageByImageTypes = images
                .Where(x => x.ImageTypeId == loggingViewColumnCoreRow.ImageTypeId &&
                            x.ImageSubtypeId == loggingViewColumnCoreRow.ImageSubtypeId)
                .Select(image => new
                {
                    image.ImageTypeId,
                    image.ImageSubtypeId,
                    CroppedImages = image.CroppedImages
                        .Where(crop => crop.Type == ImageConstSettings.ImageCropRow ||
                                       crop.Type == ImageConstSettings.ImageCropRowLower)
                }).ToList();

            var coreRow = imageByImageTypes
                .GroupBy(data => new
                {
                    ImageTypeId = data.ImageTypeId ?? 0,
                    ImageSubtypeId = data.ImageSubtypeId ?? 0
                })
                .Select(g => new CoreRowDto
                {
                    ImageType = g.Key.ImageTypeId != 0
                        ? _mapper.Map<ImageTypeDto>(loggingViewColumnCoreRow.ImageType)
                        : null,
                    ImageSubtype = g.Key.ImageSubtypeId != 0
                        ? _mapper.Map<ImageSubtypeDto>(loggingViewColumnCoreRow.ImageSubtype)
                        : null,
                    ImageCrops = g.SelectMany(data => _mapper.Map<List<ImageCropDto>>(data.CroppedImages) ?? [])
                        .ToList()
                })
                .ToList();

            coreRows.AddRange(coreRow);
        }

        var response = new
        {
            geology = MergeDataEntryGeology(dataEntryGeologySuitesDto),
            assay = assayData,
            geophysics = geophysicsData,
            coreRows
        };

        return new OkObjectResult(response);
    }

    private static void AssignValuesToDataEntries(List<DataEntryDto> dataEntryDto, List<DataEntryValueDto> values)
    {
        foreach (var value in values)
        {
            var dataEntry = dataEntryDto.FirstOrDefault(d => d.Id == value.DataEntryId);
            dataEntry?.DataEntryValues.Add(value);
        }
    }

    /// <inheritdoc />
    public async Task UpdateDataEntryValueAsync(UpdateDataEntryValueDto input)
    {
        switch (input.FieldType)
        {
            case FieldType.Colour:
            {
                if (input.ColorId == null) return;

                var data = await _colourValueRepository.FirstOrDefaultAsync(x => x.Id == input.ValueId);
                data.ColourId = input.ColorId;

                return;
            }
            case FieldType.Number:
            {
                if (input.NumberValue == null) return;

                var data = await _numberValueRepository.FirstOrDefaultAsync(x => x.Id == input.ValueId);
                data.Value = (int)input.NumberValue;

                return;
            }
            case FieldType.RockGroup:
            {
                if (input.RockTypeId == null) return;

                var data = await _rockGroupValueRepository.FirstOrDefaultAsync(x => x.Id == input.ValueId);
                data.RockTypeId = (int)input.RockTypeId;

                return;
            }
            case FieldType.PickList:
            {
                if (input.PickListItemId == null) return;

                var data = await _pickListValueRepository.FirstOrDefaultAsync(x => x.Id == input.ValueId);
                data.PickListItemId = (int)input.PickListItemId;

                return;
            }
            case FieldType.Description:
            {
                if (input.Description == null) return;

                var data = await _geologyDescriptionValueRepository.FirstOrDefaultAsync(x => x.Id == input.ValueId);
                data.Description = input.Description;

                return;
            }
            case FieldType.RockTypeNumber:
            {
                if (input.NumberValue == null) return;

                var data = await _rockTypeNumberValueRepository.FirstOrDefaultAsync(x => x.Id == input.ValueId);
                data.NumberValue = (int)input.NumberValue;

                return;
            }
            case FieldType.RockSelectNumber:
            {
                if (input.NumberValue == null && input.RockTypeId == null) return;

                var data = await _rockSelectNumberValueRepository.FirstOrDefaultAsync(x => x.Id == input.ValueId);
                data.RockTypeId = input.RockTypeId ?? data.RockTypeId;
                data.NumberValue = input.NumberValue ?? data.NumberValue;

                return;
            }
            case FieldType.Date:
            {
                if (input.DateValue == null) return;

                var data = await _geologyDateValueRepository.FirstOrDefaultAsync(x => x.Id == input.ValueId);
                data.DateValue = (DateTime)input.DateValue;

                return;
            }
            case FieldType.RockNode:
            {
                var existingRockNodeValue =
                    await _rockNodeValueRepository.FirstOrDefaultAsync(x => x.Id == input.ValueId);

                if (input.RockNodeId.HasValue)
                {
                    // Validate RockNode using the new method
                    await _rockNodeService.GetAndValidateRockNodeForTenantAsync(input.RockNodeId.Value);

                    if (existingRockNodeValue != null)
                    {
                        existingRockNodeValue.RockNodeId = input.RockNodeId.Value;
                        // await _rockNodeValueRepository.UpdateAsync(existingRockNodeValue); // UoW handles update
                    }
                    else
                    {
                        // This part of the logic might need review based on how ValueId is handled.
                        // If ValueId implies an existing RockNodeValue, creating a new one here might be wrong.
                        // The original code threw an exception here, which might still be appropriate if ValueId is for an *existing* value.
                        // For now, let's assume the original intent for this specific update path.
                        // If the goal is to create if not exists based on DataEntryId/GeologyFieldId, that's a different logic path.
                        throw new UserFriendlyException(
                            $"Cannot update RockNode: Value with ID {input.ValueId} not found, or it's not a RockNode field. To create a new RockNode value, use the appropriate creation endpoint/logic.");
                    }
                }
                else // input.RockNodeId is null, so clear/delete the value
                {
                    if (existingRockNodeValue != null)
                    {
                        await _rockNodeValueRepository.DeleteAsync(existingRockNodeValue);
                    }
                }

                return;
            }
        }
    }

    /// <inheritdoc />
    public async Task<DataEntry> UpdateDataEntryAsync(UpdateDataEntryDto input)
    {
        // Validate depth values
        if (input.DepthFrom >= input.DepthTo)
        {
            throw new UserFriendlyException("Depth From must be less than Depth To.");
        }

        var dataEntries = await _repository.GetAll()
            .AsNoTracking()
            .Where(x => x.DrillholeId == input.DrillholeId &&
                        x.GeologySuiteId == input.GeologySuiteId &&
                        x.Id != input.Id)
            .ToListAsync();

        if (dataEntries.Count != 0)
        {
            var overlapExists = dataEntries.Any(x =>
                input.DepthFrom.HasValue && x.DepthTo.HasValue && input.DepthTo.HasValue && x.DepthFrom.HasValue &&
                Math.Round(input.DepthFrom.Value, 2) < Math.Round(x.DepthTo.Value, 2) &&
                Math.Round(input.DepthTo.Value, 2) > Math.Round(x.DepthFrom.Value, 2));

            if (overlapExists)
            {
                throw new UserFriendlyException("Overlap error. The depth range overlaps with another existing range.");
            }
        }

        // Get existing DataEntry
        var dataEntry = await _repository.GetAsync(input.Id);

        // Update DataEntry fields
        dataEntry.DepthFrom = input.DepthFrom;
        dataEntry.DepthTo = input.DepthTo;
        dataEntry.ImageCropId = input.ImageCropId;
        dataEntry.X = input.X;

        // Delete existing DataEntryValues
        await _colourValueRepository.DeleteAsync(x => x.DataEntryId == input.Id);
        await _numberValueRepository.DeleteAsync(x => x.DataEntryId == input.Id);
        await _rockGroupValueRepository.DeleteAsync(x => x.DataEntryId == input.Id);
        await _pickListValueRepository.DeleteAsync(x => x.DataEntryId == input.Id);
        await _geologyDescriptionValueRepository.DeleteAsync(x => x.DataEntryId == input.Id);
        await _rockTypeNumberValueRepository.DeleteAsync(x => x.DataEntryId == input.Id);
        await _rockSelectNumberValueRepository.DeleteAsync(x => x.DataEntryId == input.Id);
        await _geologyDateValueRepository.DeleteAsync(x => x.DataEntryId == input.Id);
        await _rockNodeValueRepository.DeleteAsync(x => x.DataEntryId == input.Id);

        // Create new DataEntryValues
        foreach (var dataEntryValue in input.DataEntryValues)
        {
            switch (dataEntryValue.FieldType)
            {
                case FieldType.Colour:
                {
                    var value = new ColourValue
                    {
                        DataEntryId = dataEntry.Id,
                        GeologySuiteFieldId = dataEntryValue.GeologysuiteFieldId,
                        ColourId = dataEntryValue.ColourId,
                    };
                    await _colourValueRepository.InsertAsync(value);
                    break;
                }

                case FieldType.Number:
                {
                    var value = new NumberValue()
                    {
                        DataEntryId = dataEntry.Id,
                        GeologySuiteFieldId = dataEntryValue.GeologysuiteFieldId,
                        Value = dataEntryValue.NumberValue
                    };
                    await _numberValueRepository.InsertAsync(value);
                    break;
                }

                case FieldType.RockGroup:
                {
                    var value = new RockGroupValue()
                    {
                        DataEntryId = dataEntry.Id,
                        GeologySuiteFieldId = dataEntryValue.GeologysuiteFieldId,
                        RockTypeId = dataEntryValue.RockTypeId,
                    };
                    await _rockGroupValueRepository.InsertAsync(value);
                    break;
                }

                case FieldType.PickList:
                {
                    var value = new PickListValue()
                    {
                        DataEntryId = dataEntry.Id,
                        GeologySuiteFieldId = dataEntryValue.GeologysuiteFieldId,
                        PickListItemId = dataEntryValue.PickListItemId,
                    };
                    await _pickListValueRepository.InsertAsync(value);
                    break;
                }

                case FieldType.Description:
                {
                    var value = new GeologyDescriptionValue()
                    {
                        DataEntryId = dataEntry.Id,
                        GeologySuiteFieldId = dataEntryValue.GeologysuiteFieldId,
                        Description = dataEntryValue.Description
                    };
                    await _geologyDescriptionValueRepository.InsertAsync(value);
                    break;
                }

                case FieldType.RockTypeNumber:
                {
                    var value = new RockTypeNumberValue()
                    {
                        DataEntryId = dataEntry.Id,
                        GeologySuiteFieldId = dataEntryValue.GeologysuiteFieldId,
                        NumberValue = dataEntryValue.NumberValue,
                    };
                    await _rockTypeNumberValueRepository.InsertAsync(value);
                    break;
                }

                case FieldType.RockSelectNumber:
                {
                    var value = new RockSelectNumberValue()
                    {
                        DataEntryId = dataEntry.Id,
                        GeologySuiteFieldId = dataEntryValue.GeologysuiteFieldId,
                        RockTypeId = dataEntryValue.RockTypeId,
                        NumberValue = dataEntryValue.NumberValue
                    };
                    await _rockSelectNumberValueRepository.InsertAsync(value);
                    break;
                }

                case FieldType.Date:
                {
                    var value = new GeologyDateValue()
                    {
                        DataEntryId = dataEntry.Id,
                        GeologySuiteFieldId = dataEntryValue.GeologysuiteFieldId,
                        DateValue = dataEntryValue.DateValue,
                    };
                    await _geologyDateValueRepository.InsertAsync(value);
                    break;
                }

                case FieldType.RockNode:
                {
                    if (dataEntryValue.RockNodeId.HasValue)
                    {
                        // Validate RockNode using the new method
                        await _rockNodeService.GetAndValidateRockNodeForTenantAsync(dataEntryValue.RockNodeId.Value);

                        var value = new RockNodeValue
                        {
                            DataEntryId = dataEntry.Id,
                            GeologySuiteFieldId = dataEntryValue.GeologysuiteFieldId,
                            RockNodeId = dataEntryValue.RockNodeId.Value,
                        };
                        await _rockNodeValueRepository.InsertAsync(value);
                    }

                    break;
                }
            }
        }

        await _unitOfWorkManager.Current.SaveChangesAsync();

        var calculateLoggingBar = new CalculateLoggingBarDto
        {
            GeologySuiteId = input.GeologySuiteId,
            DrillHoleId = dataEntry.DrillholeId,
            ImageSubtypeId = input.ImageCropId
        };
        await CalculateLoggingBarAsync(calculateLoggingBar);

        return dataEntry;
    }

    /// <inheritdoc />
    public async Task UpdateDepthDataEntryAsync(UpdateDepthDataEntryDto input)
    {
        if (input.DataEntries.Count == 0)
        {
            throw new UserFriendlyException("DataEntries cannot be empty");
        }

        // Group entries by DrillholeId and GeologySuiteId for efficient validation
        var dataEntryIds = input.DataEntries.Select(x => x.Id).ToList();
        var dataEntries = await _repository.GetAll()
            .Where(x => x.DrillholeId == input.DrillholeId && x.GeologySuiteId == input.GeologySuiteId)
            .OrderBy(x => x.DepthFrom)
            .ToListAsync();

        foreach (var dataEntry in dataEntries)
        {
            foreach (var inputData in input.DataEntries.Where(inputData => inputData.Id == dataEntry.Id))
            {
                dataEntry.DepthFrom = inputData.DepthFrom;
                dataEntry.DepthTo = inputData.DepthTo;
            }
        }

        // Check for overlaps after all updates have been applied
        for (var i = 0; i < dataEntries.Count; i++)
        {
            var current = dataEntries[i];

            // Skip entries that aren't being updated
            if (!dataEntryIds.Contains(current.Id))
                continue;

            // Check against all other entries in the same group
            for (var j = 0; j < dataEntries.Count; j++)
            {
                if (i == j) continue; // Skip self-comparison

                var other = dataEntries[j];

                if (current.DepthFrom.HasValue && other.DepthTo.HasValue &&
                    current.DepthTo.HasValue && other.DepthFrom.HasValue &&
                    Math.Round(current.DepthFrom.Value, 2) < Math.Round(other.DepthTo.Value, 2) &&
                    Math.Round(current.DepthTo.Value, 2) > Math.Round(other.DepthFrom.Value, 2))
                {
                    throw new UserFriendlyException(
                        $"Overlap error. Depth ({current.DepthFrom}-{current.DepthTo}) " +
                        $"overlaps with Depth ({other.DepthFrom}-{other.DepthTo}).");
                }
            }
        }

        // Save all changes to the database
        await _unitOfWorkManager.Current.SaveChangesAsync();
    }

    /// <inheritdoc />
    public async Task UpdateDepthAsync(UpdateDepthDto input)
    {
        var dataEntry = await _repository.FirstOrDefaultAsync(x => x.Id == input.Id);
        if (dataEntry == null)
        {
            throw new EntityNotFoundException(typeof(DataEntry), input.Id);
        }

        var newDepthFrom = input.DepthFrom;
        var newDepthTo = input.DepthTo;

        // Validate based on the combination of new and existing values
        if (newDepthFrom >= newDepthTo)
        {
            throw new UserFriendlyException("Depth From must be less than Depth To.");
        }

        // Save the new values (if explicitly provided, allow null)
        dataEntry.DepthFrom = newDepthFrom;
        dataEntry.DepthTo = newDepthTo;

        var dataEntries = await _repository.GetAll()
            .AsNoTracking()
            .Where(x => x.DrillholeId == dataEntry.DrillholeId && x.GeologySuiteId == dataEntry.GeologySuiteId &&
                        x.Id != dataEntry.Id)
            .ToListAsync();
        if (dataEntries.Count != 0)
        {
            var overlapExists = dataEntries.Any(x =>
                newDepthFrom.HasValue && x.DepthTo.HasValue && newDepthTo.HasValue && x.DepthFrom.HasValue &&
                Math.Round(newDepthFrom.Value, 2) < Math.Round(x.DepthTo.Value, 2) &&
                Math.Round(newDepthTo.Value, 2) > Math.Round(x.DepthFrom.Value, 2));

            if (overlapExists)
            {
                throw new UserFriendlyException("Overlap error. The depth range overlaps with another existing range.");
            }
        }
    }

    /// <inheritdoc />
    public async Task DeleteGeologyDataPoint(EntityDto<int> input)
    {
        var dataEntry = await _repository.FirstOrDefaultAsync(x => x.Id == input.Id);

        if (dataEntry != null)
        {
            await _repository.DeleteAsync(dataEntry);
        }
    }

    /// <inheritdoc />
    public async Task<ResultUploadDataEntryDto> UploadDataEntryByImportTemplateAsync(
        UploadDataEntryByImportTemplateDto input)
    {
        if (_abpSession.TenantId == null)
        {
            throw new UserFriendlyException("The account does not have permission to perform this feature.");
        }

        var tenantId = _abpSession.GetTenantId();
        var errors = input.Errors;
        var geologySuite = await _geologySuiteService.GetAsync(new EntityDto(input.SuiteId))
                           ?? throw new UserFriendlyException("GeologySuite not found");

        var countRecord = 0;
        var uploadDataEntryDto = new CreateDataEntryDto
        {
            GeologySuiteId = input.SuiteId,
            DrillholeId = 0, // Will be updated for each row
            DataEntryValues = []
        };

        using var stream = input.ExcelFile.OpenReadStream();
        using var package = new ExcelPackage(stream);
        var worksheet = package.Workbook.Worksheets[0];
        var rowCount = worksheet.Dimension?.Rows ?? 0;
        if (rowCount <= 1)
            return new ResultUploadDataEntryDto
            {
                Count = 0,
                Errors = []
            };

        var drillHoleNameToId = input.DrillHoles.ToDictionary(d => d.Name, d => d.Id);
        var fieldMappings = input.ImportMappingTemplateFields.ToList();
        var columnMap = fieldMappings
            .Select(f => (f.FileColumnName, Index: GetColumnIndex(worksheet, f.FileColumnName)))
            .Where(x => x.Index != -1)
            .ToDictionary(x => x.FileColumnName, x => x.Index);

        // Process each row
        for (var row = 2; row <= rowCount; row++)
        {
            // Get drill hole ID
            var drillHoleName = worksheet.Cells[row, 1].Text?.Trim();
            if (string.IsNullOrEmpty(drillHoleName) ||
                !drillHoleNameToId.TryGetValue(drillHoleName, out var drillHoleId))
            {
                continue;
            }

            uploadDataEntryDto.DrillholeId = drillHoleId;
            uploadDataEntryDto.DataEntryValues = [];
            uploadDataEntryDto.DepthFrom = null;
            uploadDataEntryDto.DepthTo = null;

            // Map values using template fields
            foreach (var fieldMapping in fieldMappings)
            {
                if (!columnMap.TryGetValue(fieldMapping.FileColumnName, out var columnIndex))
                {
                    continue;
                }

                var value = worksheet.Cells[row, columnIndex].Text?.Trim();
                if (string.IsNullOrEmpty(value))
                {
                    continue;
                }

                if (fieldMapping.SystemFieldName == "Depth From")
                {
                    if (double.TryParse(value, out var depthFrom))
                    {
                        uploadDataEntryDto.DepthFrom = depthFrom;
                    }

                    continue;
                }

                if (fieldMapping.SystemFieldName == "Depth To")
                {
                    if (double.TryParse(value, out var depthTo))
                    {
                        uploadDataEntryDto.DepthTo = depthTo;
                    }

                    continue;
                }

                var field = geologySuite.GeologySuiteFields?.FirstOrDefault(f =>
                    f.Name == fieldMapping.SystemFieldName);
                if (field == null) continue;

                var entryValue = new DataEntryValueDto
                {
                    GeologysuiteFieldId = field.Id,
                    FieldType = field.GeologyField.Type
                };

                switch (field.GeologyField.Type)
                {
                    case FieldType.Number: // Done
                    case FieldType.RockTypeNumber: // Done
                        if (double.TryParse(value, out var numValue))
                            entryValue.NumberValue = numValue;
                        break;
                    case FieldType.RockSelectNumber: // Done
                    {
                        if (string.IsNullOrEmpty(value)) continue;

                        var lastDashIndex = value.LastIndexOf('-');

                        if (lastDashIndex == -1) continue;

                        var rockTypeName = value[..lastDashIndex].Trim();
                        var rockType =
                            field.GeologyField.RockSelectNumber?.RockGroup.RockTypes?.FirstOrDefault(t =>
                                t.Name == rockTypeName);
                        if (rockType != null)
                        {
                            entryValue.RockTypeId = rockType.Id;
                        }
                        else
                        {
                            errors.Add(
                                $"Rock Type {rockTypeName} not found in Rock Group {field.GeologyField.RockSelectNumber?.RockGroup.Name}.");
                        }

                        var numberValueText = value[(lastDashIndex + 1)..].Trim();
                        if (double.TryParse(numberValueText, out var numberValue))
                            entryValue.NumberValue = numberValue;

                        break;
                    }
                    case FieldType.RockGroup: // Done
                    {
                        var rockType = field.GeologyField.RockGroup?.RockTypes?.FirstOrDefault(t => t.Name == value);
                        if (rockType != null)
                        {
                            entryValue.RockTypeId = rockType.Id;
                        }
                        else
                        {
                            errors.Add(
                                $"Rock Type {value} not found in Rock Group {field.GeologyField.RockGroup?.Name}.");
                        }

                        break;
                    }
                    case FieldType.PickList: // Done
                    {
                        var pickList = field.GeologyField.PickList?.PickListItems.FirstOrDefault(t => t.Name == value);
                        if (pickList != null)
                        {
                            entryValue.PickListItemId = pickList.Id;
                        }
                        else
                        {
                            errors.Add(
                                $"Pick list item {value} not found in Pick list {field.GeologyField.PickList?.Name}.");
                        }

                        break;
                    }
                    case FieldType.Description: // Done
                        entryValue.Description = value;
                        break;
                    case FieldType.Colour: // Done
                    {
                        var color = await _colourRepository.FirstOrDefaultAsync(x =>
                            x.Name == value && x.TenantId == tenantId);
                        if (color != null)
                        {
                            entryValue.ColourId = color.Id;
                        }
                        else
                        {
                            errors.Add($"Colour {value} not found.");
                        }

                        break;
                    }
                    case FieldType.Date: // Done
                        if (DateTime.TryParse(value, out var dateValue))
                            entryValue.DateValue = dateValue;
                        break;
                    case FieldType.RockNode: // Done
                    {
                        var result = new List<TreeNodeDto>();
                        FlattenNode(field.GeologyField.TreeNode, result);

                        var rockNode = result.FirstOrDefault(x => x.Name == value);
                        if (rockNode != null)
                        {
                            entryValue.RockNodeId = rockNode.Id;
                        }
                        else
                        {
                            errors.Add($"Rock Node {value} not found in Tree Node {field.GeologyField.TreeNode.Name}.");
                        }

                        break;
                    }
                }


                if (IsValidEntryValue(entryValue, field.GeologyField.Type))
                {
                    uploadDataEntryDto.DataEntryValues.Add(entryValue);
                }
            }

            if (uploadDataEntryDto.DataEntryValues.Count != 0 || uploadDataEntryDto.DepthFrom.HasValue ||
                uploadDataEntryDto.DepthTo.HasValue)
            {
                if (errors.Count > 0) continue;
                await CreateDataEntryAsync(uploadDataEntryDto);
                countRecord++;
            }
        }

        return new ResultUploadDataEntryDto
        {
            Count = countRecord,
            Errors = errors
        };
    }

    /// <inheritdoc />
    public async Task<ResultUploadDataEntryDto> UpdateDataEntryByImportTemplateAsync(
        UploadDataEntryByImportTemplateDto input)
    {
        if (_abpSession.TenantId == null)
        {
            throw new UserFriendlyException("The account does not have permission to perform this feature.");
        }

        var tenantId = _abpSession.GetTenantId();
        var errors = new List<string>();
        var geologySuite = await _geologySuiteService.GetAsync(new EntityDto(input.SuiteId))
                           ?? throw new UserFriendlyException("GeologySuite not found");

        var countRecord = 0;
        var updateDataEntryDto = new UpdateDataEntryDto
        {
            GeologySuiteId = input.SuiteId,
            DrillholeId = 0, // Will be updated for each row
            DataEntryValues = []
        };

        using var stream = input.ExcelFile.OpenReadStream();
        using var package = new ExcelPackage(stream);
        var worksheet = package.Workbook.Worksheets[0];
        var rowCount = worksheet.Dimension?.Rows ?? 0;
        if (rowCount <= 1)
            return new ResultUploadDataEntryDto
            {
                Count = 0,
                Errors = []
            };

        var drillHoleNameToId = input.DrillHoles.ToDictionary(d => d.Name, d => d.Id);
        var fieldMappings = input.ImportMappingTemplateFields.ToList();
        var columnMap = fieldMappings
            .Select(f => (f.FileColumnName, Index: GetColumnIndex(worksheet, f.FileColumnName)))
            .Where(x => x.Index != -1)
            .ToDictionary(x => x.FileColumnName, x => x.Index);

        // Process each row
        for (var row = 2; row <= rowCount; row++)
        {
            // Get drill hole ID
            var drillHoleName = worksheet.Cells[row, 1].Text?.Trim();
            if (string.IsNullOrEmpty(drillHoleName) ||
                !drillHoleNameToId.TryGetValue(drillHoleName, out var drillHoleId))
            {
                continue;
            }

            updateDataEntryDto.DrillholeId = drillHoleId;
            updateDataEntryDto.DataEntryValues = [];
            updateDataEntryDto.DepthFrom = null;
            updateDataEntryDto.DepthTo = null;

            // Map values using template fields
            foreach (var fieldMapping in fieldMappings)
            {
                if (!columnMap.TryGetValue(fieldMapping.FileColumnName, out var columnIndex))
                {
                    continue;
                }

                var value = worksheet.Cells[row, columnIndex].Text?.Trim();
                if (string.IsNullOrEmpty(value))
                {
                    continue;
                }

                if (fieldMapping.SystemFieldName == "Depth From")
                {
                    if (double.TryParse(value, out var depthFrom))
                    {
                        updateDataEntryDto.DepthFrom = depthFrom;
                    }

                    continue;
                }

                if (fieldMapping.SystemFieldName == "Depth To")
                {
                    if (double.TryParse(value, out var depthTo))
                    {
                        updateDataEntryDto.DepthTo = depthTo;
                    }

                    continue;
                }

                var field = geologySuite.GeologySuiteFields?.FirstOrDefault(f =>
                    f.Name == fieldMapping.SystemFieldName);
                if (field == null) continue;

                var entryValue = new DataEntryValueDto
                {
                    GeologysuiteFieldId = field.Id,
                    FieldType = field.GeologyField.Type
                };

                switch (field.GeologyField.Type)
                {
                    case FieldType.Number: // Done
                    case FieldType.RockTypeNumber: // Done
                        if (double.TryParse(value, out var numValue))
                            entryValue.NumberValue = numValue;
                        break;
                    case FieldType.RockSelectNumber: // Done
                    {
                        if (string.IsNullOrEmpty(value)) continue;

                        var lastDashIndex = value.LastIndexOf('-');

                        if (lastDashIndex == -1) continue;

                        var rockTypeName = value[..lastDashIndex].Trim();
                        var rockType =
                            field.GeologyField.RockSelectNumber?.RockGroup.RockTypes?.FirstOrDefault(t =>
                                t.Name == rockTypeName);
                        if (rockType != null)
                        {
                            entryValue.RockTypeId = rockType.Id;
                        }
                        else
                        {
                            errors.Add(
                                $"Rock Type {rockTypeName} not found in Rock Group {field.GeologyField.RockSelectNumber?.RockGroup.Name}.");
                        }

                        var numberValueText = value[(lastDashIndex + 1)..].Trim();
                        if (double.TryParse(numberValueText, out var numberValue))
                            entryValue.NumberValue = numberValue;

                        break;
                    }
                    case FieldType.RockGroup: // Done
                    {
                        var rockType = field.GeologyField.RockGroup?.RockTypes?.FirstOrDefault(t => t.Name == value);
                        if (rockType != null)
                        {
                            entryValue.RockTypeId = rockType.Id;
                        }
                        else
                        {
                            errors.Add(
                                $"Rock Type {value} not found in Rock Group {field.GeologyField.RockGroup?.Name}.");
                        }

                        break;
                    }
                    case FieldType.PickList: // Done
                    {
                        var pickList = field.GeologyField.PickList?.PickListItems.FirstOrDefault(t => t.Name == value);
                        if (pickList != null)
                        {
                            entryValue.PickListItemId = pickList.Id;
                        }
                        else
                        {
                            errors.Add(
                                $"Pick list item {value} not found in Pick list {field.GeologyField.PickList?.Name}.");
                        }

                        break;
                    }
                    case FieldType.Description: // Done
                        entryValue.Description = value;
                        break;
                    case FieldType.Colour: // Done
                    {
                        var color = await _colourRepository.FirstOrDefaultAsync(x =>
                            x.Name == value && x.TenantId == tenantId);
                        if (color != null)
                        {
                            entryValue.ColourId = color.Id;
                        }
                        else
                        {
                            errors.Add($"Colour {value} not found.");
                        }

                        break;
                    }
                    case FieldType.Date: // Done
                        if (DateTime.TryParse(value, out var dateValue))
                            entryValue.DateValue = dateValue;
                        break;
                    case FieldType.RockNode: // Done
                    {
                        var result = new List<TreeNodeDto>();
                        FlattenNode(field.GeologyField.TreeNode, result);

                        var rockNode = result.FirstOrDefault(x => x.Name == value);
                        if (rockNode != null)
                        {
                            entryValue.RockNodeId = rockNode.Id;
                        }
                        else
                        {
                            errors.Add($"Rock Node {value} not found in Tree Node {field.GeologyField.TreeNode.Name}.");
                        }

                        break;
                    }
                }

                if (IsValidEntryValue(entryValue, field.GeologyField.Type))
                {
                    updateDataEntryDto.DataEntryValues.Add(entryValue);
                }
            }

            // if (!updateDataEntryDto.DepthFrom.HasValue || !updateDataEntryDto.DepthTo.HasValue)
            // {
            //     continue;
            // }

            // Find existing DataEntry by DrillholeId, DepthFrom, and DepthTo
            var existingDataEntry = await _repository.FirstOrDefaultAsync(de =>
                de.DrillholeId == updateDataEntryDto.DrillholeId &&
                de.GeologySuiteId == geologySuite.Id &&
                de.DepthFrom.HasValue && updateDataEntryDto.DepthFrom.HasValue &&
                de.DepthTo.HasValue && updateDataEntryDto.DepthTo.HasValue &&
                Math.Abs(de.DepthFrom.Value - updateDataEntryDto.DepthFrom.Value) < 0.01 &&
                Math.Abs(de.DepthTo.Value - updateDataEntryDto.DepthTo.Value) < 0.01);

            if (existingDataEntry == null)
            {
                // Optionally log or skip if no matching DataEntry is found
                continue;
            }

            // Update the existing DataEntry
            updateDataEntryDto.Id = existingDataEntry.Id;
            await UpdateDataEntryAsync(updateDataEntryDto);
            countRecord++;
        }

        return new ResultUploadDataEntryDto
        {
            Count = countRecord,
            Errors = errors
        };
    }

    /// <inheritdoc />
    public async Task<ResultUploadDataEntryDto> AddAndUpdateDataEntryByImportTemplateAsync(
        UploadDataEntryByImportTemplateDto input)
    {
        if (_abpSession.TenantId == null)
        {
            throw new UserFriendlyException("The account does not have permission to perform this feature.");
        }

        var tenantId = _abpSession.GetTenantId();
        var errors = new List<string>();
        var geologySuite = await _geologySuiteService.GetAsync(new EntityDto(input.SuiteId))
                           ?? throw new UserFriendlyException("GeologySuite not found");

        var countRecord = 0;
        var createDataEntryDto = new CreateDataEntryDto
        {
            GeologySuiteId = input.SuiteId,
            DrillholeId = 0,
            DataEntryValues = []
        };
        var updateDataEntryDto = new UpdateDataEntryDto
        {
            GeologySuiteId = input.SuiteId,
            DrillholeId = 0,
            DataEntryValues = []
        };

        using var stream = input.ExcelFile.OpenReadStream();
        using var package = new ExcelPackage(stream);
        var worksheet = package.Workbook.Worksheets[0];
        var rowCount = worksheet.Dimension?.Rows ?? 0;
        if (rowCount <= 1)
            return new ResultUploadDataEntryDto
            {
                Count = 0,
                Errors = []
            };

        var drillHoleNameToId = input.DrillHoles.ToDictionary(d => d.Name, d => d.Id);
        var fieldMappings = input.ImportMappingTemplateFields.ToList();
        var columnMap = fieldMappings
            .Select(f => (f.FileColumnName, Index: GetColumnIndex(worksheet, f.FileColumnName)))
            .Where(x => x.Index != -1)
            .ToDictionary(x => x.FileColumnName, x => x.Index);

        // Process each row
        for (var row = 2; row <= rowCount; row++)
        {
            // Get drill hole ID
            var drillHoleName = worksheet.Cells[row, 1].Text?.Trim();
            if (string.IsNullOrEmpty(drillHoleName) ||
                !drillHoleNameToId.TryGetValue(drillHoleName, out var drillHoleId))
            {
                continue;
            }

            createDataEntryDto.DrillholeId = drillHoleId;
            createDataEntryDto.DataEntryValues = [];
            createDataEntryDto.DepthFrom = null;
            createDataEntryDto.DepthTo = null;
            updateDataEntryDto.DrillholeId = drillHoleId;
            updateDataEntryDto.DataEntryValues = [];
            updateDataEntryDto.DepthFrom = null;
            updateDataEntryDto.DepthTo = null;

            // Map values using template fields
            foreach (var fieldMapping in fieldMappings)
            {
                if (!columnMap.TryGetValue(fieldMapping.FileColumnName, out var columnIndex))
                {
                    continue;
                }

                var value = worksheet.Cells[row, columnIndex].Text?.Trim();
                if (string.IsNullOrEmpty(value))
                {
                    continue;
                }

                if (fieldMapping.SystemFieldName == "Depth From")
                {
                    if (double.TryParse(value, out var depthFrom))
                    {
                        updateDataEntryDto.DepthFrom = depthFrom;
                        createDataEntryDto.DepthFrom = depthFrom;
                    }

                    continue;
                }

                if (fieldMapping.SystemFieldName == "Depth To")
                {
                    if (double.TryParse(value, out var depthTo))
                    {
                        updateDataEntryDto.DepthTo = depthTo;
                        createDataEntryDto.DepthTo = depthTo;
                    }

                    continue;
                }

                var field = geologySuite.GeologySuiteFields?.FirstOrDefault(f =>
                    f.Name == fieldMapping.SystemFieldName);
                if (field == null) continue;

                var entryValue = new DataEntryValueDto
                {
                    GeologysuiteFieldId = field.Id,
                    FieldType = field.GeologyField.Type
                };

                switch (field.GeologyField.Type)
                {
                    case FieldType.Number: // Done
                    case FieldType.RockTypeNumber: // Done
                        if (double.TryParse(value, out var numValue))
                            entryValue.NumberValue = numValue;
                        break;
                    case FieldType.RockSelectNumber: // Done
                    {
                        if (string.IsNullOrEmpty(value)) continue;

                        var lastDashIndex = value.LastIndexOf('-');

                        if (lastDashIndex == -1) continue;

                        var rockTypeName = value[..lastDashIndex].Trim();
                        var rockType =
                            field.GeologyField.RockSelectNumber?.RockGroup.RockTypes?.FirstOrDefault(t =>
                                t.Name == rockTypeName);
                        if (rockType != null)
                        {
                            entryValue.RockTypeId = rockType.Id;
                        }
                        else
                        {
                            errors.Add(
                                $"Rock Type {rockTypeName} not found in Rock Group {field.GeologyField.RockSelectNumber?.RockGroup.Name}.");
                        }

                        var numberValueText = value[(lastDashIndex + 1)..].Trim();
                        if (double.TryParse(numberValueText, out var numberValue))
                            entryValue.NumberValue = numberValue;

                        break;
                    }
                    case FieldType.RockGroup: // Done
                    {
                        var rockType = field.GeologyField.RockGroup?.RockTypes?.FirstOrDefault(t => t.Name == value);
                        if (rockType != null)
                        {
                            entryValue.RockTypeId = rockType.Id;
                        }
                        else
                        {
                            errors.Add(
                                $"Rock Type {value} not found in Rock Group {field.GeologyField.RockGroup?.Name}.");
                        }

                        break;
                    }
                    case FieldType.PickList: // Done
                    {
                        var pickList = field.GeologyField.PickList?.PickListItems.FirstOrDefault(t => t.Name == value);
                        if (pickList != null)
                        {
                            entryValue.PickListItemId = pickList.Id;
                        }
                        else
                        {
                            errors.Add(
                                $"Pick list item {value} not found in Pick list {field.GeologyField.PickList?.Name}.");
                        }

                        break;
                    }
                    case FieldType.Description: // Done
                        entryValue.Description = value;
                        break;
                    case FieldType.Colour: // Done
                    {
                        var color = await _colourRepository.FirstOrDefaultAsync(x =>
                            x.Name == value && x.TenantId == tenantId);
                        if (color != null)
                        {
                            entryValue.ColourId = color.Id;
                        }
                        else
                        {
                            errors.Add($"Colour {value} not found.");
                        }

                        break;
                    }
                    case FieldType.Date: // Done
                        if (DateTime.TryParse(value, out var dateValue))
                            entryValue.DateValue = dateValue;
                        break;
                    case FieldType.RockNode: // Done
                    {
                        var result = new List<TreeNodeDto>();
                        FlattenNode(field.GeologyField.TreeNode, result);

                        var rockNode = result.FirstOrDefault(x => x.Name == value);
                        if (rockNode != null)
                        {
                            entryValue.RockNodeId = rockNode.Id;
                        }
                        else
                        {
                            errors.Add($"Rock Node {value} not found in Tree Node {field.GeologyField.TreeNode.Name}.");
                        }

                        break;
                    }
                }

                if (IsValidEntryValue(entryValue, field.GeologyField.Type))
                {
                    createDataEntryDto.DataEntryValues.Add(entryValue);
                    updateDataEntryDto.DataEntryValues.Add(entryValue);
                }
            }

            // Skip if no valid data to process
            // if (!updateDataEntryDto.DepthFrom.HasValue || !updateDataEntryDto.DepthTo.HasValue)
            // {
            //     continue;
            // }

            // Find existing DataEntry by DrillholeId, DepthFrom, and DepthTo
            var existingDataEntry = await _repository.FirstOrDefaultAsync(de =>
                de.DrillholeId == updateDataEntryDto.DrillholeId &&
                de.GeologySuiteId == geologySuite.Id &&
                de.DepthFrom.HasValue && updateDataEntryDto.DepthFrom.HasValue &&
                de.DepthTo.HasValue && updateDataEntryDto.DepthTo.HasValue &&
                Math.Abs(de.DepthFrom.Value - updateDataEntryDto.DepthFrom.Value) < 0.01 &&
                Math.Abs(de.DepthTo.Value - updateDataEntryDto.DepthTo.Value) < 0.01);

            if (existingDataEntry != null)
            {
                // Update existing DataEntry
                updateDataEntryDto.Id = existingDataEntry.Id;
                await UpdateDataEntryAsync(updateDataEntryDto);
            }
            else
            {
                // Create new DataEntry
                await CreateDataEntryAsync(createDataEntryDto);
            }

            countRecord++;
        }

        return new ResultUploadDataEntryDto
        {
            Count = countRecord,
            Errors = errors
        };
    }

    /// <inheritdoc />
    public async Task<List<string>> ValidationDataEntryDataAsync(UploadDataEntryByImportTemplateDto input)
    {
        ExcelPackage.LicenseContext = LicenseContext.NonCommercial;

        var fileExtension = Path.GetExtension(input.ExcelFile.FileName).ToLower();

        var errors = new List<string>();
        var fieldMappings = input.ImportMappingTemplateFields.ToList();
        var depthRangesByDrillHole = new Dictionary<string, List<(double DepthFrom, double DepthTo, int Row)>>();

        using (var stream = new MemoryStream())
        {
            await input.ExcelFile.CopyToAsync(stream);
            stream.Position = 0;

            switch (fileExtension)
            {
                case ".xlsx":
                {
                    using var package = new ExcelPackage(stream);
                    var worksheet = package.Workbook.Worksheets[0];
                    var rowCount = worksheet.Dimension?.Rows ?? 0;

                    // Get column indices from headers based on mapping
                    var drillHoleField = fieldMappings.FirstOrDefault(f => f.SystemFieldName == "DrillHole");
                    var depthFromField = fieldMappings.FirstOrDefault(f => f.SystemFieldName == "Depth From");
                    var depthToField = fieldMappings.FirstOrDefault(f => f.SystemFieldName == "Depth To");

                    if (drillHoleField == null || depthFromField == null || depthToField == null)
                    {
                        errors.Add("Required mapping fields 'DrillHole', 'Depth From', 'Depth To' not found.");
                        return errors;
                    }

                    var columnMap = fieldMappings
                        .Select(f => (f.FileColumnName, Index: GetColumnIndex(worksheet, f.FileColumnName)))
                        .Where(x => x.Index != -1)
                        .ToDictionary(x => x.FileColumnName, x => x.Index);

                    if (!columnMap.TryGetValue(drillHoleField.FileColumnName, out var drillHoleColIndex))
                    {
                        errors.Add($"Could not find column '{drillHoleField.FileColumnName}' in Excel file.");
                        return errors;
                    }

                    if (!columnMap.TryGetValue(depthFromField.FileColumnName, out var depthFromColIndex))
                    {
                        errors.Add($"Could not find column '{depthFromField.FileColumnName}' in Excel file.");
                        return errors;
                    }

                    if (!columnMap.TryGetValue(depthToField.FileColumnName, out var depthToColIndex))
                    {
                        errors.Add($"Could not find column '{depthToField.FileColumnName}' in Excel file.");
                        return errors;
                    }

                    // Process each row
                    for (var row = 2; row <= rowCount; row++)
                    {
                        var drillHoleNameFile = worksheet.Cells[row, drillHoleColIndex].Text.Trim();
                        var depthFromText = worksheet.Cells[row, depthFromColIndex].Text.Trim();
                        var depthToText = worksheet.Cells[row, depthToColIndex].Text.Trim();

                        if (!double.TryParse(depthFromText, out var depthFromValue) || depthFromValue < 0)
                        {
                            errors.Add($"Depth From value at row {row} must be a number greater than 0.");
                            continue;
                        }

                        if (!double.TryParse(depthToText, out var depthToValue) || depthToValue < 0)
                        {
                            errors.Add($"Depth To value at row {row} must be a number greater than 0.");
                            continue;
                        }

                        // Validate DepthFrom <= DepthTo
                        if (Math.Round(depthFromValue, 2) > Math.Round(depthToValue, 2))
                        {
                            errors.Add(
                                $"Depth From ({depthFromValue}) is greater than Depth To ({depthToValue}) at row {row}.");
                            continue;
                        }

                        // Store valid depth ranges for overlap and gap checking
                        if (!depthRangesByDrillHole.TryGetValue(drillHoleNameFile, out var value))
                        {
                            value = new List<(double, double, int)>();
                            depthRangesByDrillHole[drillHoleNameFile] = value;
                        }

                        value.Add((depthFromValue, depthToValue, row));
                    }

                    break;
                }
                case ".csv":
                {
                    using var reader = new StreamReader(stream);
                    var headers = (await reader.ReadLineAsync())?.Split(',').Select(h => h.Trim()).ToList();
                    if (headers == null || headers.Count == 0)
                    {
                        errors.Add("CSV file has no headers.");
                        return errors;
                    }

                    var drillHoleField = fieldMappings.FirstOrDefault(f => f.SystemFieldName == "DrillHole");
                    var depthFromField = fieldMappings.FirstOrDefault(f => f.SystemFieldName == "Depth From");
                    var depthToField = fieldMappings.FirstOrDefault(f => f.SystemFieldName == "Depth To");

                    if (drillHoleField == null || depthFromField == null || depthToField == null)
                    {
                        errors.Add("Required mapping fields 'DrillHole', 'Depth From', 'Depth To' not found.");
                        return errors;
                    }

                    var drillHoleColIndex = headers.FindIndex(h => h == drillHoleField.FileColumnName);
                    var depthFromColIndex = headers.FindIndex(h => h == depthFromField.FileColumnName);
                    var depthToColIndex = headers.FindIndex(h => h == depthToField.FileColumnName);

                    if (drillHoleColIndex == -1)
                    {
                        errors.Add($"Could not find column '{drillHoleField.FileColumnName}' in CSV file.");
                        return errors;
                    }

                    if (depthFromColIndex == -1)
                    {
                        errors.Add($"Could not find column '{depthFromField.FileColumnName}' in CSV file.");
                        return errors;
                    }

                    if (depthToColIndex == -1)
                    {
                        errors.Add($"Could not find column '{depthToField.FileColumnName}' in CSV file.");
                        return errors;
                    }

                    var row = 2;
                    while (await reader.ReadLineAsync() is { } line)
                    {
                        var values = line.Split(',').Select(v => v.Trim()).ToArray();
                        if (values.Length <= Math.Max(Math.Max(drillHoleColIndex, depthFromColIndex), depthToColIndex))
                        {
                            errors.Add($"Row {row} has insufficient columns.");
                            row++;
                            continue;
                        }

                        var drillHoleNameFile = values[drillHoleColIndex];
                        var depthFromText = values[depthFromColIndex];
                        var depthToText = values[depthToColIndex];

                        if (!double.TryParse(depthFromText, out var depthFromValue) || depthFromValue < 0)
                        {
                            errors.Add($"Depth From value at row {row} must be a number greater than 0.");
                            row++;
                            continue;
                        }

                        if (!double.TryParse(depthToText, out var depthToValue) || depthToValue < 0)
                        {
                            errors.Add($"Depth To value at row {row} must be a number greater than 0.");
                            row++;
                            continue;
                        }

                        // Validate DepthFrom <= DepthTo
                        if (depthFromValue > depthToValue)
                        {
                            errors.Add(
                                $"Depth From ({depthFromValue}) is greater than Depth To ({depthToValue}) at row {row}.");
                            row++;
                            continue;
                        }

                        if (!depthRangesByDrillHole.TryGetValue(drillHoleNameFile, out var value))
                        {
                            value = new List<(double, double, int)>();
                            depthRangesByDrillHole[drillHoleNameFile] = value;
                        }

                        value.Add((depthFromValue, depthToValue, row));

                        row++;
                    }

                    break;
                }
                default:
                {
                    errors.Add("Unsupported file format. Please upload a .xlsx or .csv file.");
                    break;
                }
            }
        }

        var drillHoleNames = depthRangesByDrillHole.Keys.ToList();
        var dataEntries = await _repository.GetAllIncluding(x => x.DrillHole)
            .AsNoTracking()
            .Where(x => x.GeologySuiteId == input.SuiteId &&
                        drillHoleNames.Contains(x.DrillHole.Name))
            .Select(x => new
            {
                x.DepthFrom,
                x.DepthTo
            })
            .ToListAsync();

        // Check for overlaps and gaps per drillhole
        foreach (var drillHoleEntry in depthRangesByDrillHole)
        {
            var drillHoleName = drillHoleEntry.Key;
            var ranges = drillHoleEntry.Value;

            // Sort ranges by DepthFrom, then DepthTo for consistent checking
            ranges.Sort((a, b) => Math.Abs(a.DepthFrom - b.DepthFrom) < 0.01
                ? a.DepthTo.CompareTo(b.DepthTo)
                : a.DepthFrom.CompareTo(b.DepthFrom));

            // Check each range against the next one for overlaps and gaps
            for (var i = 0; i < ranges.Count - 1; i++)
            {
                var current = ranges[i];
                var next = ranges[i + 1];

                // Check for overlap
                if (Math.Round(current.DepthTo, 2) > Math.Round(next.DepthFrom, 2))
                {
                    errors.Add(
                        $"Overlap detected for DrillHole '{drillHoleName}' between rows {current.Row} ({current.DepthFrom}-{current.DepthTo}) and {next.Row} ({next.DepthFrom}-{next.DepthTo}).");
                }
            }

            if (input.ImportMode == ImportMode.Add)
            {
                foreach (var current in ranges)
                {
                    foreach (var bItem in dataEntries)
                    {
                        if (bItem.DepthFrom.HasValue && bItem.DepthTo.HasValue &&
                            Math.Abs(Math.Round(current.Item1, 2) - Math.Round(bItem.DepthFrom.Value, 2)) < 0.01 &&
                            Math.Abs(Math.Round(current.Item2, 2) - Math.Round(bItem.DepthTo.Value, 2)) < 0.01)
                        {
                            errors.Add(
                                $"Duplicate detected for DrillHole '{drillHoleName}' between rows {current.Item3}: DepthFrom {current.Item1}, DepthTo: {current.Item2}");
                            break;
                        }
                    }
                }
            }
        }

        return errors;
    }

    /// <inheritdoc />
    public async Task DeleteDataToolAsync(DeleteDataToolDto input)
    {
        switch (input.SuiteType)
        {
            case DeleteDataSuiteType.Geology:
            {
                var geologySuite = await _geologySuiteRepository.GetAll()
                    .AsNoTracking()
                    .Include(x => x.GeologySuiteFields)
                    .ThenInclude(x => x.GeologyField)
                    .FirstOrDefaultAsync(x => x.Id == input.SuiteId);
                var geologySuiteFields = geologySuite?.GeologySuiteFields
                    .WhereIf(input.FieldId.HasValue, x => x.Id == input.FieldId)
                    .ToList() ?? [];
                foreach (var geologySuiteField in geologySuiteFields)
                {
                    switch (geologySuiteField.GeologyField.Type)
                    {
                        case FieldType.Colour:
                        {
                            await _colourValueRepository.DeleteAsync(x =>
                                x.GeologySuiteFieldId == geologySuiteField.Id);
                            break;
                        }
                        case FieldType.Number:
                        {
                            await _numberValueRepository.DeleteAsync(x =>
                                x.GeologySuiteFieldId == geologySuiteField.Id);
                            break;
                        }
                        case FieldType.RockGroup:
                        {
                            await _rockGroupValueRepository.DeleteAsync(x =>
                                x.GeologySuiteFieldId == geologySuiteField.Id);
                            break;
                        }
                        case FieldType.PickList:
                        {
                            await _pickListValueRepository.DeleteAsync(x =>
                                x.GeologySuiteFieldId == geologySuiteField.Id);
                            break;
                        }
                        case FieldType.Description:
                        {
                            await _geologyDescriptionValueRepository.DeleteAsync(x =>
                                x.GeologySuiteFieldId == geologySuiteField.Id);
                            break;
                        }
                        case FieldType.RockTypeNumber:
                        {
                            await _rockTypeNumberValueRepository.DeleteAsync(x =>
                                x.GeologySuiteFieldId == geologySuiteField.Id);
                            break;
                        }
                        case FieldType.RockSelectNumber:
                        {
                            await _rockSelectNumberValueRepository.DeleteAsync(x =>
                                x.GeologySuiteFieldId == geologySuiteField.Id);
                            break;
                        }
                        case FieldType.Date:
                        {
                            await _geologyDateValueRepository.DeleteAsync(x =>
                                x.GeologySuiteFieldId == geologySuiteField.Id);
                            break;
                        }
                        case FieldType.RockNode:
                        {
                            await _rockNodeValueRepository.DeleteAsync(x =>
                                x.GeologySuiteFieldId == geologySuiteField.Id);
                            break;
                        }
                    }
                }

                break;
            }
            case DeleteDataSuiteType.Assay:
            {
                var assaySuite = await _assaySuiteRepository.GetAll()
                    .AsNoTracking()
                    .Include(x => x.AssaySuiteAttributes)
                    .ThenInclude(x => x.AssayAttribute)
                    .FirstOrDefaultAsync(x => x.Id == input.SuiteId);

                if (input.FieldId.HasValue && input.FieldId != 0)
                {
                    var assaySuiteField = assaySuite?.AssaySuiteAttributes
                        .FirstOrDefault(x => x.AssayAttributeId == input.FieldId);
                    if (assaySuiteField != null)
                    {
                        await _assayDataRepository.DeleteAsync(x =>
                            x.DrillHoleId == input.DrillHoleId && x.AssaySuiteId == assaySuiteField.AssaySuiteId &&
                            x.AttributeName == assaySuiteField.AssayAttribute.Name);
                    }
                }
                else
                {
                    var groupIds = await _assayDataRepository.GetAll()
                        .Where(x =>
                            x.DrillHoleId == input.DrillHoleId && x.AssaySuiteId == input.SuiteId)
                        .Select(x => x.GroupId)
                        .Distinct()
                        .ToListAsync();

                    foreach (var groupId in groupIds)
                    {
                        await _assayDataRepository.DeleteAsync(x => x.GroupId == groupId);
                    }
                }

                break;
            }
            case DeleteDataSuiteType.Geophysics:
            {
                var suite = await _suiteRepository.GetAll()
                    .AsNoTracking()
                    .Include(x => x.SuiteAttributes)
                    .ThenInclude(x => x.Attribute)
                    .FirstOrDefaultAsync(x => x.Id == input.SuiteId);

                if (input.FieldId.HasValue && input.FieldId != 0)
                {
                    var suiteAttribute = suite?.SuiteAttributes
                        .FirstOrDefault(x => x.Id == input.FieldId);
                    if (suiteAttribute != null)
                    {
                        await _geophysicsDataRepository.DeleteAsync(x =>
                            x.DrillHoleId == input.DrillHoleId && x.SuiteId == suiteAttribute.SuiteId &&
                            x.AttributeName == suiteAttribute.Attribute.Name);
                    }
                }
                else
                {
                    var groupIds = await _geophysicsDataRepository.GetAll()
                        .Where(x =>
                            x.DrillHoleId == input.DrillHoleId && x.SuiteId == input.SuiteId)
                        .Select(x => x.GroupId)
                        .Distinct()
                        .ToListAsync();

                    foreach (var groupId in groupIds)
                    {
                        await _geophysicsDataRepository.DeleteAsync(x => x.GroupId == groupId);
                    }
                }

                break;
            }
            case DeleteDataSuiteType.Geotech:
            {
                if (input.FieldId.HasValue && input.FieldId != 0)
                {
                    await _geotechDataRepository.DeleteAsync(x =>
                        x.GeotechSuiteId == input.SuiteId && x.StructureId == input.FieldId);
                }
                else
                {
                    await _geotechDataRepository.DeleteAsync(x => x.GeotechSuiteId == input.SuiteId);
                }

                break;
            }
            default:
                throw new UserFriendlyException("Unknown suite type.");
        }
    }

    private static DataEntryGeologySuiteDto MergeDataEntryGeology(List<DataEntryGeologySuiteDto> input)
    {
        return new DataEntryGeologySuiteDto
        {
            TotalCount = input.Sum(x => x.TotalCount),
            DataEntries = input.SelectMany(x => x.DataEntries).ToList()
        };
    }

    private async Task<List<DataEntryValueDto>> FetchAllDataEntryValuesAsync(List<int> dataEntryIds)
    {
        var values = new List<DataEntryValueDto>();

        // Fetch Color Values
        var colorValues = await _colourValueRepository.GetAllIncluding(x => x.Colour, x => x.GeologySuiteField)
            .AsNoTracking()
            .Where(x => dataEntryIds.Contains(x.DataEntryId) && x.GeologySuiteField.IsActive)
            .Select(x => new DataEntryValueDto
            {
                DataEntryId = x.DataEntryId,
                ValueId = x.Id,
                GeologysuiteFieldId = x.GeologySuiteFieldId,
                Sequence = x.GeologySuiteField.Sequence,
                FieldName = x.GeologySuiteField.Name,
                FieldType = FieldType.Colour,
                ColourId = x.ColourId,
                Colour = x.Colour != null ? _mapper.Map<ColourDto>(x.Colour) : null
            })
            .ToListAsync();
        values.AddRange(colorValues);

        // Fetch Number Values
        var numberValues = await _numberValueRepository.GetAll()
            .AsNoTracking()
            .Include(x => x.GeologySuiteField)
            .ThenInclude(x => x.GeologyField)
            .ThenInclude(x => x.Number)
            .ThenInclude(x => x.Unit)
            .Where(x => dataEntryIds.Contains(x.DataEntryId) && x.GeologySuiteField.IsActive)
            .Select(x => new DataEntryValueDto
            {
                DataEntryId = x.DataEntryId,
                ValueId = x.Id,
                GeologysuiteFieldId = x.GeologySuiteFieldId,
                Sequence = x.GeologySuiteField.Sequence,
                FieldName = x.GeologySuiteField.Name,
                FieldType = FieldType.Number,
                NumberValue = x.Value,
                Number = _mapper.Map<NumberDto>(x.GeologySuiteField.GeologyField.Number)
            })
            .ToListAsync();
        values.AddRange(numberValues);

        // Fetch Rock Group Values
        var rockGroupValues = await _rockGroupValueRepository
            .GetAllIncluding(x => x.RockType, x => x.GeologySuiteField)
            .AsNoTracking()
            .Where(x => dataEntryIds.Contains(x.DataEntryId) && x.GeologySuiteField.IsActive)
            .Select(x => new DataEntryValueDto
            {
                DataEntryId = x.DataEntryId,
                ValueId = x.Id,
                GeologysuiteFieldId = x.GeologySuiteFieldId,
                Sequence = x.GeologySuiteField.Sequence,
                FieldName = x.GeologySuiteField.Name,
                FieldType = FieldType.RockGroup,
                RockTypeId = x.RockTypeId,
                RockType = x.RockType != null
                    ? new RockTypeDto
                    {
                        Id = x.RockType.Id,
                        Name = x.RockType.Name,
                        Code = x.RockType.Code,
                        Description = x.RockType.Description,
                        IsActive = x.RockType.IsActive,
                        RockStyle = _mapper.Map<RockStyleDto>(x.RockType.RockStyle)
                    }
                    : null
            })
            .ToListAsync();
        values.AddRange(rockGroupValues);

        // Fetch Picklist Values
        var pickListValues = await _pickListValueRepository
            .GetAllIncluding(x => x.PickListItem, x => x.GeologySuiteField)
            .AsNoTracking()
            .Where(x => dataEntryIds.Contains(x.DataEntryId) && x.GeologySuiteField.IsActive)
            .Select(x => new DataEntryValueDto
            {
                DataEntryId = x.DataEntryId,
                ValueId = x.Id,
                GeologysuiteFieldId = x.GeologySuiteFieldId,
                Sequence = x.GeologySuiteField.Sequence,
                FieldName = x.GeologySuiteField.Name,
                FieldType = FieldType.PickList,
                PickListItemId = x.PickListItemId,
                PickListItem = x.PickListItem != null ? _mapper.Map<PickListItemDto>(x.PickListItem) : null
            })
            .ToListAsync();
        values.AddRange(pickListValues);

        // Fetch Description Values
        var descriptionValues = await _geologyDescriptionValueRepository.GetAllIncluding(x => x.GeologySuiteField)
            .AsNoTracking()
            .Where(x => dataEntryIds.Contains(x.DataEntryId) && x.GeologySuiteField.IsActive)
            .Select(x => new DataEntryValueDto
            {
                DataEntryId = x.DataEntryId,
                ValueId = x.Id,
                GeologysuiteFieldId = x.GeologySuiteFieldId,
                Sequence = x.GeologySuiteField.Sequence,
                FieldName = x.GeologySuiteField.Name,
                FieldType = FieldType.Description,
                Description = x.Description
            })
            .ToListAsync();
        values.AddRange(descriptionValues);

        // Fetch Rock Type Number Values
        var rockTypeNumberValues = await _rockTypeNumberValueRepository.GetAll()
            .AsNoTracking()
            .Include(x => x.GeologySuiteField)
            .ThenInclude(x => x.GeologyField)
            .ThenInclude(x => x.RockTypeNumber)
            .ThenInclude(x => x.RockType)
            .ThenInclude(x => x.RockStyle)
            .Include(x => x.GeologySuiteField)
            .ThenInclude(x => x.GeologyField)
            .ThenInclude(x => x.RockTypeNumber)
            .ThenInclude(x => x.Number)
            .ThenInclude(x => x.Unit)
            .Where(x => dataEntryIds.Contains(x.DataEntryId) && x.GeologySuiteField.IsActive)
            .ToListAsync();
        var rockTypeNumberResult = rockTypeNumberValues.Select(x => new DataEntryValueDto
        {
            DataEntryId = x.DataEntryId,
            ValueId = x.Id,
            GeologysuiteFieldId = x.GeologySuiteFieldId,
            Sequence = x.GeologySuiteField.Sequence,
            FieldName = x.GeologySuiteField.Name,
            FieldType = FieldType.RockTypeNumber,
            NumberValue = x.NumberValue,
            Number = _mapper.Map<NumberDto>(x.GeologySuiteField.GeologyField.RockTypeNumber?.Number),
            RockType = _mapper.Map<RockTypeDto>(x.GeologySuiteField.GeologyField.RockTypeNumber?.RockType)
        }).ToList();
        values.AddRange(rockTypeNumberResult);

        // Fetch Rock Select Number Values
        var rockSelectNumberValues = await _rockSelectNumberValueRepository.GetAllIncluding(x => x.RockType)
            .AsNoTracking()
            .Include(x => x.GeologySuiteField)
            .ThenInclude(x => x.GeologyField)
            .ThenInclude(x => x.RockSelectNumber)
            .ThenInclude(x => x.Number)
            .ThenInclude(x => x.Unit)
            .Where(x => dataEntryIds.Contains(x.DataEntryId) && x.GeologySuiteField.IsActive)
            .Select(x => new DataEntryValueDto
            {
                DataEntryId = x.DataEntryId,
                ValueId = x.Id,
                GeologysuiteFieldId = x.GeologySuiteFieldId,
                Sequence = x.GeologySuiteField.Sequence,
                FieldName = x.GeologySuiteField.Name,
                FieldType = FieldType.RockSelectNumber,
                RockTypeId = x.RockTypeId,
                RockType = x.RockType != null
                    ? new RockTypeDto
                    {
                        Id = x.RockType.Id,
                        Name = x.RockType.Name,
                        Code = x.RockType.Code,
                        Description = x.RockType.Description,
                        IsActive = x.RockType.IsActive,
                        RockStyle = _mapper.Map<RockStyleDto>(x.RockType.RockStyle)
                    }
                    : null,
                NumberValue = x.NumberValue,
                Number = x.GeologySuiteField.GeologyField.RockSelectNumber != null
                    ? _mapper.Map<NumberDto>(x.GeologySuiteField.GeologyField.RockSelectNumber.Number)
                    : null
            })
            .ToListAsync();
        values.AddRange(rockSelectNumberValues);

        // Fetch Date Values
        var dateValues = await _geologyDateValueRepository.GetAllIncluding(x => x.GeologySuiteField)
            .AsNoTracking()
            .Where(x => dataEntryIds.Contains(x.DataEntryId) && x.GeologySuiteField.IsActive)
            .Select(x => new DataEntryValueDto
            {
                DataEntryId = x.DataEntryId,
                ValueId = x.Id,
                GeologysuiteFieldId = x.GeologySuiteFieldId,
                Sequence = x.GeologySuiteField.Sequence,
                FieldName = x.GeologySuiteField.Name,
                FieldType = FieldType.Date,
                DateValue = x.DateValue
            })
            .ToListAsync();
        values.AddRange(dateValues);

        // Fetch Rock Node Values
        var rockNodeValues = await _rockNodeValueRepository
            .GetAllIncluding(x => x.GeologySuiteField, x => x.RockNode)
            .AsNoTracking()
            .Include(x => x.RockNode)
            .ThenInclude(x => x.RockType)
            .Where(x => dataEntryIds.Contains(x.DataEntryId) && x.GeologySuiteField.IsActive)
            .Select(x => new DataEntryValueDto
            {
                DataEntryId = x.DataEntryId,
                ValueId = x.Id,
                GeologysuiteFieldId = x.GeologySuiteFieldId,
                Sequence = x.GeologySuiteField.Sequence,
                FieldName = x.GeologySuiteField.Name,
                FieldType = FieldType.RockNode,
                RockNodeId = x.RockNodeId,
                RockNode = x.RockNode != null
                    ? new RockNodeDto
                    {
                        Id = x.RockNode.Id,
                        Name = x.RockNode.Name,
                        Code = x.RockNode.Code,
                        Description = x.RockNode.Description,
                        NodeType = x.RockNode.NodeType,
                        IsActive = x.RockNode.IsActive,
                        DisplayColor = x.RockNode.DisplayColor,
                        IconUrl = x.RockNode.IconUrl,
                        RockType = _mapper.Map<RockTypeDto>(x.RockNode.RockType)
                    }
                    : null,
            })
            .ToListAsync();
        values.AddRange(rockNodeValues);

        return values;
    }

    private async Task BuildTreeRecursivelyAsync(TreeNodeDto parentNode, int tenantId, HashSet<int> visitedNodes)
    {
        // Add current node to visited set to prevent cycles
        if (!visitedNodes.Add(parentNode.Id))
        {
            return;
        }

        // Get child nodes for this parent from the relations
        var childRelations = await _rockNodeRelationRepository.GetAllIncluding(r => r.ChildNode)
            .Where(r => r.ParentNodeId == parentNode.Id &&
                        r.ChildNode.TenantId == tenantId)
            .OrderBy(r => r.DisplayOrder)
            .ToListAsync();

        foreach (var relation in childRelations)
        {
            // Skip if we've already visited this node (prevents cycles)
            if (visitedNodes.Contains(relation.ChildNodeId)) continue;

            // Get the full child node entity
            var childNode = await _rockNodeRepository.GetAsync(relation.ChildNodeId);
            if (childNode.TenantId != tenantId) continue; // Additional tenant safety check

            var childTreeNode = new TreeNodeDto
            {
                Id = childNode.Id,
                Name = childNode.Name,
                Code = childNode.Code,
                Description = childNode.Description,
                NodeType = childNode.NodeType,
                RockTypeId = childNode.RockTypeId,
                IsActive = childNode.IsActive,
                DisplayColor = childNode.DisplayColor,
                IconUrl = childNode.IconUrl,
                ParentId = parentNode.Id
            };

            // Recursively add children to this child node
            await BuildTreeRecursivelyAsync(childTreeNode, tenantId, [..visitedNodes]);

            // Add child to parent's children list
            parentNode.Children.Add(childTreeNode);
        }
    }

    private static int GetColumnIndex(ExcelWorksheet worksheet, string columnName)
    {
        var columnCount = worksheet.Dimension?.Columns ?? 0;
        for (var col = 1; col <= columnCount; col++)
        {
            if (worksheet.Cells[1, col].Text?.Trim() == columnName.Trim())
            {
                return col;
            }
        }

        return -1;
    }

    private static bool IsValidEntryValue(DataEntryValueDto entryValue, FieldType fieldType)
    {
        return fieldType switch
        {
            FieldType.Number or FieldType.RockTypeNumber or FieldType.RockSelectNumber => entryValue.NumberValue
                .HasValue,
            FieldType.RockGroup => entryValue.RockTypeId.HasValue,
            FieldType.PickList => entryValue.PickListItemId.HasValue,
            FieldType.Description => !string.IsNullOrEmpty(entryValue.Description),
            FieldType.Colour => entryValue.ColourId.HasValue,
            FieldType.Date => entryValue.DateValue.HasValue,
            FieldType.RockNode => entryValue.RockNodeId.HasValue,
            _ => false
        };
    }

    private static void FlattenNode(TreeNodeDto node, List<TreeNodeDto> result)
    {
        result.Add(new TreeNodeDto
        {
            Id = node.Id,
            Name = node.Name,
            Children = [] // Not including Children in flattened result
        });

        // Recursively process children
        if (node.Children.Count <= 0) return;
        foreach (var child in node.Children)
        {
            FlattenNode(child, result);
        }
    }

    /// <inheritdoc />
    public async Task CalculateLoggingBarAsync(CalculateLoggingBarDto input)
    {
        var drillHole = await _drillholeRepository.GetAllIncluding(x => x.Project)
                            .AsNoTracking()
                            .Select(x => new { x.Id, x.Project.CoreTrayLength })
                            .FirstOrDefaultAsync(x => x.Id == input.DrillHoleId)
                        ?? throw new EntityNotFoundException(typeof(DrillHole), input.DrillHoleId);

        var coreTray = drillHole.CoreTrayLength;

        var dataEntries = await _repository.GetAll()
            .AsNoTracking()
            .Where(x => x.DrillholeId == input.DrillHoleId && x.GeologySuiteId == input.GeologySuiteId)
            .OrderBy(x => x.DepthFrom)
            .ToListAsync();
        if (dataEntries.Count == 0)
        {
            return;
        }

        var imageQuery = await _imageRepository.GetAllIncluding(x => x.ImageType, x => x.ImageSubtype)
            .AsNoTracking()
            .Include(x => x.CroppedImages)
            .ThenInclude(x => x.RockLines)
            .Where(x => x.DrillHoleId == input.DrillHoleId && x.ImageType != null && x.ImageType.IsStandard)
            .WhereIf(input.ImageSubtypeId.HasValue, x => x.ImageSubtypeId == input.ImageSubtypeId)
            .OrderBy(x => x.DepthFrom)
            .ToListAsync();

        var wetImages = imageQuery.Where(x => x.ImageSubtype is { IsWet: true }).ToList();
        var dryImages = imageQuery.Where(x => x.ImageSubtype is { IsDry: true }).ToList();
        var uvImages = imageQuery.Where(x => x.ImageSubtype is { IsUv: true }).ToList();

        List<Image> images;
        var imageSubtypeId = input.ImageSubtypeId;
        if (input.ImageSubtypeId.HasValue)
        {
            images = imageQuery;
            if (images.Count == 0)
            {
                throw new UserFriendlyException($"No images found for subtype ID {input.ImageSubtypeId}.");
            }
        }
        else
        {
            if (wetImages.Count != 0)
            {
                images = wetImages;
                imageSubtypeId = wetImages.First().ImageSubtypeId;
            }
            else if (dryImages.Count != 0)
            {
                images = dryImages;
                imageSubtypeId = dryImages.First().ImageSubtypeId;
            }
            else if (uvImages.Count != 0)
            {
                images = uvImages;
                imageSubtypeId = uvImages.First().ImageSubtypeId;
            }
            else
            {
                return;
            }
        }

        if (imageSubtypeId == null || images.Count == 0)
        {
            return;
        }

        await _loggingBarRepository.DeleteAsync(x =>
            x.GeologySuiteId == input.GeologySuiteId &&
            x.DrillHoleId == input.DrillHoleId &&
            x.ImageSubtypeId == imageSubtypeId);

        var rockLines = images.SelectMany(x => x.CroppedImages)
            .SelectMany(x => x.RockLines)
            .Where(rockLine => rockLine.Type == RockLineType.Recovery)
            .Select(rockLine => new RockLineDto
            {
                Id = rockLine.Id,
                Type = rockLine.Type,
                DepthFrom = rockLine.DepthFrom,
                DepthTo = rockLine.DepthTo,
                StartX = rockLine.StartX,
                EndX = rockLine.EndX,
                RowIndex = rockLine.RowIndex,
                ImageCropId = rockLine.ImageCropId
            })
            .ToList();

        var wetImageViewCalcDepths = images.Select(x => new ImageViewCalcDepthDto
        {
            Id = x.Id,
            DepthFrom = x.DepthFrom,
            DepthTo = x.DepthTo,
            OcrResult = x.OcrResult,
            SegmentResult = x.SegmentResult,
            CroppedImages = x.CroppedImages
                .Where(c => c.Type is ImageConstSettings.ImageCropRow or ImageConstSettings.ImageCropRowLower)
                .OrderBy(c => c.DepthFrom)
                .Select(c => new CroppedImageViewCalcDepthDto
                {
                    Id = c.Id,
                    Coordinate = c.Coordinate,
                    Type = c.Type,
                    DepthFrom = c.DepthFrom
                })
                .ToList()
        }).ToList();

        var imageCropDetails = new List<ImageCropDetailDto>();

        foreach (var rockLine in rockLines)
        {
            var startX = rockLine.StartX;
            var endX = rockLine.EndX;

            // Get the minimum StartX from all rock lines in the first row
            var startPoint = new CalculateDepthInListImageDto
            {
                Images = wetImageViewCalcDepths,
                ImageCropId = rockLine.ImageCropId,
                X = startX,
                CoreTray = coreTray
            };

            // Get the maximum EndX from all rock lines in the last row
            var endPoint = new CalculateDepthInListImageDto
            {
                Images = wetImageViewCalcDepths,
                ImageCropId = rockLine.ImageCropId,
                X = endX,
                CoreTray = coreTray
            };

            var startDepth = _calculationDepthService.CalculateDepthInListImage(startPoint);
            var endDepth = _calculationDepthService.CalculateDepthInListImage(endPoint);
            if (startDepth == null || endDepth == null) continue;

            imageCropDetails.Add(new ImageCropDetailDto
            {
                RockLineId = rockLine.Id,
                ImageCropId = rockLine.ImageCropId,
                StartDepth = (double)startDepth,
                StartX = startX,
                EndDepth = (double)endDepth,
                EndX = endX,
            });
        }

        if (imageCropDetails.Count == 0)
        {
            return;
        }

        var detailRockLines = imageCropDetails.OrderBy(x => x.StartDepth).ToList();
        var firstRockLine = detailRockLines.First();
        var lastRockLine = detailRockLines.Last();

        var loggingBars = new List<LoggingBar>();
        foreach (var dataEntry in dataEntries)
        {
            var startDepth = dataEntry.DepthFrom;
            var endDepth = dataEntry.DepthTo;

            if (startDepth == null || endDepth == null) continue;

            var startImageCrop =
                detailRockLines.LastOrDefault(x =>
                    x.StartDepth <= startDepth && startDepth <= x.EndDepth && x.StartDepth < x.EndDepth);
            var endImageCrop = detailRockLines.LastOrDefault(x =>
                x.StartDepth <= endDepth && endDepth <= x.EndDepth && x.StartDepth < x.EndDepth);

            if (startImageCrop == null && endImageCrop == null) continue;
            if (startImageCrop == null && endImageCrop != null)
            {
                startImageCrop = FindItemByDepth((double)startDepth, detailRockLines);
            }

            if (startImageCrop != null && endImageCrop == null)
            {
                endImageCrop = FindItemByDepth((double)endDepth, detailRockLines);
            }

            if (startImageCrop != null && endImageCrop != null)
            {
                var checkFirst = startImageCrop.RockLineId == firstRockLine.RockLineId;
                var checkLast = endImageCrop.RockLineId == lastRockLine.RockLineId;

                var startX = InterpolateX(startImageCrop.StartDepth, startImageCrop.StartX, startImageCrop.EndDepth,
                    startImageCrop.EndX, (double)startDepth, checkFirst, checkLast);
                var endX = InterpolateX(endImageCrop.StartDepth, endImageCrop.StartX, endImageCrop.EndDepth,
                    endImageCrop.EndX, (double)endDepth, checkFirst, checkLast);

                loggingBars.Add(new LoggingBar
                {
                    GeologySuiteId = input.GeologySuiteId,
                    DrillHoleId = input.DrillHoleId,
                    ImageSubtypeId = (int)imageSubtypeId,
                    DepthFrom = (double)startDepth,
                    StartImageCropId = startImageCrop.ImageCropId,
                    StartX = startX,
                    DepthTo = (double)endDepth,
                    EndImageCropId = endImageCrop.ImageCropId,
                    EndX = endX,
                    BetweenImageCropIds = detailRockLines
                        .Where(x =>
                            x.StartDepth > startImageCrop.StartDepth &&
                            x.EndDepth < endImageCrop.EndDepth &&
                            x.ImageCropId != startImageCrop.ImageCropId &&
                            x.ImageCropId != endImageCrop.ImageCropId)
                        .Select(x => x.ImageCropId)
                        .Distinct()
                        .ToList(),
                    DataEntryId = dataEntry.Id
                });
            }
        }

        await _loggingBarRepository.InsertRangeAsync(loggingBars);
    }

    private static ImageCropDetailDto FindItemByDepth(double inputDepth, List<ImageCropDetailDto> items)
    {
        // Step 1: Check for item where inputDepth is in range [StartDepth, EndDepth]
        var imageCropDetail = items.LastOrDefault(item => inputDepth >= item.StartDepth && inputDepth <= item.EndDepth);
        if (imageCropDetail != null) return imageCropDetail;

        // Step 2: Find item with closest StartDepth or EndDepth
        var minDistance = double.MaxValue;
        var closestItem = new ImageCropDetailDto();

        foreach (var item in items)
        {
            var distanceToStart = Math.Abs(inputDepth - item.StartDepth);
            var distanceToEnd = Math.Abs(inputDepth - item.EndDepth);
            var minItemDistance = Math.Min(distanceToStart, distanceToEnd);

            if (minItemDistance < minDistance)
            {
                minDistance = minItemDistance;
                closestItem = item;
            }
        }

        return closestItem;
    }

    /// <inheritdoc />
    public async Task<PagedResultDto<LoggingBarDto>> GetAllLoggingBarAsync(PagedLoggingBarRequestDto input)
    {
        var query = _loggingBarRepository.GetAll()
            .AsNoTracking()
            .Where(x =>
                x.GeologySuiteId == input.GeologySuiteId &&
                x.DrillHoleId == input.DrillholeId &&
                x.ImageSubtypeId == input.ImageSubtypeId)
            .WhereIf(input.DepthFrom.HasValue, x => x.DepthFrom >= input.DepthFrom)
            .WhereIf(input.DepthTo.HasValue, x => x.DepthTo <= input.DepthTo)
            .OrderBy(x => x.DepthFrom);

        var totalCount = await query.CountAsync();

        var loggingBars = await query
            .Skip(input.SkipCount)
            .Take(input.MaxResultCount)
            .ToListAsync();

        var loggingBarDtos = loggingBars.Select(x => new LoggingBarDto
        {
            Id = x.Id,
            GeologySuiteId = x.GeologySuiteId,
            DrillHoleId = x.DrillHoleId,
            ImageSubtypeId = x.ImageSubtypeId,
            DepthFrom = x.DepthFrom,
            StartImageCropId = x.StartImageCropId,
            StartX = x.StartX,
            DepthTo = x.DepthTo,
            EndImageCropId = x.EndImageCropId,
            EndX = x.EndX,
            BetweenImageCropIds = x.BetweenImageCropIds,
            DataEntryId = x.DataEntryId,
            DataEntryValues = []
        }).ToList();

        // Get all DataEntryIds to fetch values for
        var dataEntryIds = loggingBars.Where(x => x.DataEntryId > 0).Select(x => x.DataEntryId).ToList();
        if (dataEntryIds.Count == 0)
        {
            return new PagedResultDto<LoggingBarDto>(totalCount, loggingBarDtos);
        }

        // Get rock group values for all DataEntryIds
        var rockGroupValues = await _rockGroupValueRepository
            .GetAllIncluding(x => x.RockType, x => x.GeologySuiteField)
            .AsNoTracking()
            .Where(x => dataEntryIds.Contains(x.DataEntryId) &&
                        x.GeologySuiteField.IsActive &&
                        x.GeologySuiteField.GeologyField.Type == FieldType.RockGroup)
            .Select(x => new DataEntryValueDto
            {
                DataEntryId = x.DataEntryId,
                ValueId = x.Id,
                GeologysuiteFieldId = x.GeologySuiteFieldId,
                Sequence = x.GeologySuiteField.Sequence,
                FieldName = x.GeologySuiteField.Name,
                FieldType = FieldType.RockGroup,
                RockTypeId = x.RockTypeId,
                RockType = x.RockType != null
                    ? new RockTypeDto
                    {
                        Id = x.RockType.Id,
                        Name = x.RockType.Name,
                        Code = x.RockType.Code,
                        Description = x.RockType.Description,
                        IsActive = x.RockType.IsActive,
                        RockStyle = _mapper.Map<RockStyleDto>(x.RockType.RockStyle)
                    }
                    : null
            })
            .ToListAsync();

        // Assign rock group values to corresponding LoggingBarDtos
        foreach (var rockGroupValue in rockGroupValues)
        {
            var loggingBarDto = loggingBarDtos.FirstOrDefault(x => x.DataEntryId == rockGroupValue.DataEntryId);
            loggingBarDto?.DataEntryValues.Add(rockGroupValue);
        }

        return new PagedResultDto<LoggingBarDto>(totalCount, loggingBarDtos);
    }


    /// <inheritdoc />
    public async Task<DataEntryDto> GetDetailDataEntry(int dataEntryId)
    {
        // Validate and get the data entry
        var dataEntry = await _repository.GetAllIncluding(x => x.DrillHole)
            .AsNoTracking()
            .FirstOrDefaultAsync(x => x.Id == dataEntryId);

        if (dataEntry == null)
        {
            throw new EntityNotFoundException(typeof(DataEntry), dataEntryId);
        }

        // Check tenant access
        if (_abpSession.TenantId.HasValue && dataEntry.DrillHole.TenantId != _abpSession.GetTenantId())
        {
            throw new EntityNotFoundException(typeof(DataEntry), dataEntryId);
        }

        var dataEntryDto = _mapper.Map<DataEntryDto>(dataEntry);

        // Get geology suite fields for this data entry's geology suite
        var geologySuiteFields = await _geologyFieldRepository.GetAll()
            .AsNoTracking()
            .Include(x => x.GeologyField)
            .ThenInclude(x => x.RockNode)
            .Where(x => x.GeologySuiteId == dataEntry.GeologySuiteId)
            .Select(x => new DataEntryValueDto
            {
                GeologysuiteFieldId = x.Id,
                Sequence = x.Sequence,
                FieldName = x.Name,
                FieldType = x.GeologyField.Type,
            })
            .ToListAsync();

        // Handle the data entry using existing logic
        await HandleGetDataEntryAsync(geologySuiteFields, [dataEntryDto]);

        return dataEntryDto;
    }

    /// <inheritdoc />
    public async Task<BlockResultBySelectedPointDto> GetBlockBySelectedPointAsync(GetBlockBySelectedPointDto input)
    {
        var geologySuite = await _geologySuiteRepository.FirstOrDefaultAsync(x => x.Id == input.GeologySuiteId);
        if (geologySuite == null)
        {
            throw new EntityNotFoundException(typeof(GeologySuite), input.GeologySuiteId);
        }

        if (!geologySuite.IsGeotech)
        {
            throw new UserFriendlyException($"Geology Suite {geologySuite.Name} is not a Geotech.");
        }

        var images = await _imageRepository.GetAllIncluding(x => x.ImageType)
            .AsNoTracking()
            .Where(x => x.DrillHoleId == input.DrillHoleId && x.ImageType != null && x.ImageType.IsStandard)
            .OrderBy(x => x.DepthFrom)
            .Select(x => new ImageViewCalcDepthDto
            {
                Id = x.Id,
                DepthFrom = x.DepthFrom,
                DepthTo = x.DepthTo,
                OcrResult = x.OcrResult,
                SegmentResult = x.SegmentResult,
                CroppedImages = x.CroppedImages
                    .Where(c => c.Type == ImageConstSettings.ImageCropRow ||
                                c.Type == ImageConstSettings.ImageCropRowLower)
                    .OrderBy(c => c.DepthFrom)
                    .Select(c => new CroppedImageViewCalcDepthDto
                    {
                        Id = c.Id,
                        Coordinate = c.Coordinate,
                        Type = c.Type,
                        DepthFrom = c.DepthFrom
                    })
                    .ToList()
            })
            .ToListAsync();

        var imageIndexSelected = images.FindIndex(x => x.CroppedImages.Any(img => img.Id == input.ImageCropId));
        if (imageIndexSelected == -1)
        {
            throw new UserFriendlyException("Selected image crop not found.");
        }

        var previousIndex = images
            .Take(imageIndexSelected)
            .LastOrDefault(x => !string.IsNullOrEmpty(x.OcrResult))
            ?.GetIndex(images) ?? 0;

        var nextIndex = images
            .Skip(imageIndexSelected + 1)
            .FirstOrDefault(x => !string.IsNullOrEmpty(x.OcrResult))
            ?.GetIndex(images) ?? (images.Count - 1);

        var imagesFilter = images.GetRange(previousIndex, nextIndex - previousIndex + 1);

        var depthFromMin = imagesFilter.Min(x => x.DepthFrom);
        var depthToMax = imagesFilter.Max(x => x.DepthTo);

        var boundingSegmentOcrs = new List<BoundingSegmentOcrDto>();
        foreach (var image in imagesFilter)
        {
            var cropRowResults = new List<CropRowResultDto>();
            var imageCrops = image.CroppedImages.ToList();

            // Parse JSON data once outside of loops
            var ocrResult = JsonConvert.DeserializeObject<List<OcrResultV2Dto>>(image.OcrResult ?? "[]") ?? [];
            var segmentResult = JsonConvert.DeserializeObject<List<SegmentResultDto>>(image.SegmentResult ?? "[]") ??
                                [];

            // Pre-filter segment cores (avoiding repeated filtering in the loop)
            var segmentCoreRaws = segmentResult
                .Where(x => x.Class == "core")
                .Select(x => new SegmentResultDto
                {
                    Class = x.Class,
                    rowIndex = x.rowIndex,
                    BoundingSegment = x.BoundingSegment
                }).ToList();

            var segmentCores = ProcessOcrItems(ocrResult, segmentCoreRaws);


            // Create start and end blocks in bulk rather than one by one
            var newOcrBlocks = new List<OcrResultV2Dto>();
            foreach (var segment in segmentCores)
            {
                newOcrBlocks.Add(new OcrResultV2Dto
                {
                    type = "node",
                    x = segment.BoundingSegment.X,
                    originalX = segment.BoundingSegment.X,
                    y = segment.BoundingSegment.Y,
                    width = 1,
                    height = 1,
                    rowIndex = segment.rowIndex,
                    text = $"{image.DepthFrom}",
                });

                newOcrBlocks.Add(new OcrResultV2Dto
                {
                    type = "node",
                    x = segment.BoundingSegment.X + segment.BoundingSegment.Width,
                    originalX = segment.BoundingSegment.X + segment.BoundingSegment.Width,
                    y = segment.BoundingSegment.Y,
                    width = 1,
                    height = 1,
                    rowIndex = segment.rowIndex,
                    text = $"{image.DepthFrom}",
                });
            }

            ocrResult.AddRange(newOcrBlocks);

            // Pre-filter and sort OCRs once
            var ocrs = ocrResult
                .Where(x => double.TryParse(x.text, out var value) && value >= depthFromMin && value <= depthToMax)
                .OrderBy(x => x.rowIndex)
                .ThenBy(x => x.x)
                .ToList();

            var ocrWithoutWooden = new List<OcrResultV2Dto>(ocrs.Count);
            if (ocrs.Count > 0 && ocrs[0].type == "node" && boundingSegmentOcrs.Count == 0)
            {
                ocrWithoutWooden.Add(ocrs[0]);
            }

            // Process nodes in one pass
            for (var i = 1; i < ocrs.Count; i++)
            {
                if (ocrs[i].type != "node") continue;

                var prevWooden = ocrs[i - 1];
                var hasNext = i + 1 < ocrs.Count;
                var nextWooden = hasNext ? ocrs[i + 1] : null;

                var isPrevWooden = prevWooden.type.Equals("wooden", StringComparison.OrdinalIgnoreCase);
                var isNextWooden =
                    hasNext && nextWooden?.type.Equals("wooden", StringComparison.OrdinalIgnoreCase) == true;

                if (!isPrevWooden && !isNextWooden) continue;

                if (isPrevWooden)
                {
                    ocrs[i].text = prevWooden.text;
                    ocrWithoutWooden.Add(ocrs[i]);
                }
                else if (isNextWooden)
                {
                    ocrs[i].text = nextWooden.text;
                    ocrWithoutWooden.Add(ocrs[i]);
                }
            }

            // Dictionary to cache OCR results by row index for faster lookup
            var ocrResultsByRowIndex = ocrWithoutWooden
                .GroupBy(x => x.rowIndex)
                .ToDictionary(g => g.Key, g => g.ToList());

            // Process image crops and build crop row results
            for (var i = 0; i < imageCrops.Count; i++)
            {
                var coordinateImageCrop =
                    JsonConvert.DeserializeObject<CoordinateBoundingDto>(imageCrops[i].Coordinate ?? "")
                    ?? new CoordinateBoundingDto();

                // Get OCR results for current row index efficiently
                ocrResultsByRowIndex.TryGetValue(i, out var ocrResults);
                ocrResults ??= [];

                if (imageCrops[i].Id == input.ImageCropId)
                {
                    var selectedX = input.X + coordinateImageCrop.X;
                    var selectedY = coordinateImageCrop.Y + coordinateImageCrop.Height / 2;

                    var rowIndex = CalculationDepthHandler.IsPointInAnyPolygon(segmentCores, selectedX, selectedY);

                    if (rowIndex == null)
                    {
                        return new BlockResultBySelectedPointDto();
                    }

                    var createOcr = new OcrResultV2Dto
                    {
                        id = Guid.NewGuid().ToString(),
                        type = "selected",
                        x = selectedX,
                        originalX = selectedX,
                        y = selectedY,
                        width = 100,
                        height = 100,
                        text = $"{image.DepthFrom}",
                        probability = 1,
                        rowIndex = rowIndex,
                        ImageCropId = input.ImageCropId,
                    };
                    ocrResults.Add(createOcr);
                }

                // Pre-filter segment cores by row index
                var rowSegments = segmentCores
                    .Where(x => x.rowIndex == i)
                    .Select(x => x.BoundingSegment)
                    .ToList();

                var cropRowResult = new CropRowResultDto
                {
                    ImageCropId = imageCrops[i].Id,
                    X = coordinateImageCrop.X,
                    Y = coordinateImageCrop.Y,
                    Width = coordinateImageCrop.Width,
                    Height = coordinateImageCrop.Height,
                    RowIndex = i,
                    OcrResults = ocrResults.OrderBy(x => x.x).ToList(),
                    BoundingSegment = rowSegments
                };
                cropRowResults.Add(cropRowResult);
            }

            // Process all crop rows at once to build boundingSegmentOcr
            var boundingSegmentOcr = new List<BoundingSegmentOcrDto>();
            foreach (var crop in cropRowResults)
            {
                // Create a spatial index for OCR results to speed up matching
                var ocrByXCoord = crop.OcrResults.ToList();

                foreach (var segment in crop.BoundingSegment)
                {
                    // More efficient matching using a single pass filter
                    var matched = ocrByXCoord
                        .Where(ocr => ocr.x >= segment.X && ocr.x <= (segment.X + segment.Width))
                        .ToList();

                    boundingSegmentOcr.Add(new BoundingSegmentOcrDto
                    {
                        X = segment.X,
                        Y = segment.Y,
                        Width = segment.Width,
                        Height = segment.Height,
                        RowIndex = crop.RowIndex,
                        OcrResults = matched
                    });
                }
            }

            // Sort once before adding to the main collection
            boundingSegmentOcrs.AddRange(boundingSegmentOcr.OrderBy(x => x.RowIndex).ThenBy(x => x.X));
        }


        // Process filterBoundingSegmentOcrs in one pass
        var filterBoundingSegmentOcrs = boundingSegmentOcrs
            .Select(x => new BoundingSegmentOcrDto
            {
                X = 0, // x.X - x.X is always 0
                Y = x.Y,
                Width = x.Width,
                Height = x.Height,
                RowIndex = x.RowIndex,
                OcrResults = x.OcrResults.Select(o => new OcrResultV2Dto
                {
                    id = o.id,
                    type = o.type,
                    x = o.x - x.X,
                    originalX = o.originalX,
                    y = o.y,
                    width = o.width,
                    height = o.height,
                    text = o.text,
                    rowIndex = o.rowIndex
                }).ToList()
            })
            .ToList();

        var alignBoundingSegmentOcrs = CalculationDepthHandler.AlignBoundingSegmentOcrs(filterBoundingSegmentOcrs);

        // Combine these operations to reduce iterations
        var horizontalOcrBlocks = alignBoundingSegmentOcrs
            .SelectMany(row => row.OcrResults)
            .Where(item => double.TryParse(item.text, NumberStyles.Any, CultureInfo.InvariantCulture, out _))
            .Select(item => new OcrBlockDto
            {
                X = item.x,
                OriginalX = item.originalX,
                Y = item.y,
                Width = item.width,
                Height = item.height,
                Value = double.Parse(item.text, CultureInfo.InvariantCulture),
                Type = item.type
            })
            .ToList();

        var selectedIndex = horizontalOcrBlocks.FindIndex(b => b.Type == "selected");

        if (selectedIndex < 1) return new BlockResultBySelectedPointDto();

        var before = horizontalOcrBlocks[selectedIndex - 1];
        var after = horizontalOcrBlocks[selectedIndex + 1];

        var dataEntries = await _repository.GetAll()
            .AsNoTracking()
            .Where(x => x.GeologySuiteId == input.GeologySuiteId)
            .ToListAsync();
        var dataEntryExist = dataEntries.FirstOrDefault(x => x.DepthFrom == before.Value && x.DepthTo == after.Value);

        return new BlockResultBySelectedPointDto
        {
            BlockFrom = before.Value,
            BlockTo = after.Value,
            DataEntryId = dataEntryExist?.Id,
        };
    }

    /// <inheritdoc />
    public async Task<double?> GetNextBlockAsync(GetNextBlockDto input)
    {
        var geologySuite = await _geologySuiteRepository.FirstOrDefaultAsync(x => x.Id == input.GeologySuiteId);
        if (geologySuite == null)
        {
            throw new EntityNotFoundException(typeof(GeologySuite), input.GeologySuiteId);
        }

        if (!geologySuite.IsGeotech)
        {
            throw new UserFriendlyException($"Geology Suite {geologySuite.Name} is not a Geotech.");
        }

        var images = await _imageRepository.GetAllIncluding(x => x.ImageType)
            .AsNoTracking()
            .Where(x => x.DrillHoleId == input.DrillHoleId && x.ImageType != null && x.ImageType.IsStandard)
            .OrderBy(x => x.DepthFrom)
            .Select(x => new ImageViewCalcDepthDto
            {
                Id = x.Id,
                DepthFrom = x.DepthFrom,
                DepthTo = x.DepthTo,
                OcrResult = x.OcrResult,
                SegmentResult = x.SegmentResult,
                CroppedImages = x.CroppedImages
                    .Where(c => c.Type == ImageConstSettings.ImageCropRow ||
                                c.Type == ImageConstSettings.ImageCropRowLower)
                    .OrderBy(c => c.DepthFrom)
                    .Select(c => new CroppedImageViewCalcDepthDto
                    {
                        Id = c.Id,
                        Coordinate = c.Coordinate,
                        Type = c.Type,
                        DepthFrom = c.DepthFrom
                    })
                    .ToList()
            })
            .ToListAsync();

        if (images.Count == 0)
        {
            return null;
        }

        var depthFromMin = images.Min(x => x.DepthFrom);
        var depthToMax = images.Max(x => x.DepthTo);

        var boundingSegmentOcrs = new List<BoundingSegmentOcrDto>();
        foreach (var image in images)
        {
            var cropRowResults = new List<CropRowResultDto>();
            var imageCrops = image.CroppedImages.ToList();

            // Parse JSON data once outside of loops
            var ocrResult = JsonConvert.DeserializeObject<List<OcrResultV2Dto>>(image.OcrResult ?? "[]") ?? [];
            var segmentResult = JsonConvert.DeserializeObject<List<SegmentResultDto>>(image.SegmentResult ?? "[]") ??
                                [];

            // Pre-filter segment cores (avoiding repeated filtering in the loop)
            var segmentCoreRaws = segmentResult
                .Where(x => x.Class == "core")
                .Select(x => new SegmentResultDto
                {
                    Class = x.Class,
                    rowIndex = x.rowIndex,
                    BoundingSegment = x.BoundingSegment
                }).ToList();

            var segmentCores = ProcessOcrItems(ocrResult, segmentCoreRaws);


            // Create start and end blocks in bulk rather than one by one
            var newOcrBlocks = new List<OcrResultV2Dto>();
            foreach (var segment in segmentCores)
            {
                newOcrBlocks.Add(new OcrResultV2Dto
                {
                    type = "node",
                    x = segment.BoundingSegment.X,
                    originalX = segment.BoundingSegment.X,
                    y = segment.BoundingSegment.Y,
                    width = 1,
                    height = 1,
                    rowIndex = segment.rowIndex,
                    text = $"{image.DepthFrom}",
                });

                newOcrBlocks.Add(new OcrResultV2Dto
                {
                    type = "node",
                    x = segment.BoundingSegment.X + segment.BoundingSegment.Width,
                    originalX = segment.BoundingSegment.X + segment.BoundingSegment.Width,
                    y = segment.BoundingSegment.Y,
                    width = 1,
                    height = 1,
                    rowIndex = segment.rowIndex,
                    text = $"{image.DepthFrom}",
                });
            }

            ocrResult.AddRange(newOcrBlocks);

            // Pre-filter and sort OCRs once
            var ocrs = ocrResult
                .Where(x => double.TryParse(x.text, out var value) && value >= depthFromMin && value <= depthToMax)
                .OrderBy(x => x.rowIndex)
                .ThenBy(x => x.x)
                .ToList();

            var ocrWithoutWooden = new List<OcrResultV2Dto>(ocrs.Count);
            if (ocrs.Count > 0 && ocrs[0].type == "node" && boundingSegmentOcrs.Count == 0)
            {
                ocrWithoutWooden.Add(ocrs[0]);
            }

            // Process nodes in one pass
            for (var i = 1; i < ocrs.Count; i++)
            {
                if (ocrs[i].type != "node") continue;

                var prevWooden = ocrs[i - 1];
                var hasNext = i + 1 < ocrs.Count;
                var nextWooden = hasNext ? ocrs[i + 1] : null;

                var isPrevWooden = prevWooden.type.Equals("wooden", StringComparison.OrdinalIgnoreCase);
                var isNextWooden =
                    hasNext && nextWooden?.type.Equals("wooden", StringComparison.OrdinalIgnoreCase) == true;

                if (!isPrevWooden && !isNextWooden) continue;

                if (isPrevWooden)
                {
                    ocrs[i].text = prevWooden.text;
                    ocrWithoutWooden.Add(ocrs[i]);
                }
                else if (isNextWooden)
                {
                    ocrs[i].text = nextWooden.text;
                    ocrWithoutWooden.Add(ocrs[i]);
                }
            }

            // Dictionary to cache OCR results by row index for faster lookup
            var ocrResultsByRowIndex = ocrWithoutWooden
                .GroupBy(x => x.rowIndex)
                .ToDictionary(g => g.Key, g => g.ToList());

            // Process image crops and build crop row results
            for (var i = 0; i < imageCrops.Count; i++)
            {
                var coordinateImageCrop =
                    JsonConvert.DeserializeObject<CoordinateBoundingDto>(imageCrops[i].Coordinate ?? "")
                    ?? new CoordinateBoundingDto();

                // Get OCR results for current row index efficiently
                ocrResultsByRowIndex.TryGetValue(i, out var ocrResults);
                ocrResults ??= [];

                // Pre-filter segment cores by row index
                var rowSegments = segmentCores
                    .Where(x => x.rowIndex == i)
                    .Select(x => x.BoundingSegment)
                    .ToList();

                var cropRowResult = new CropRowResultDto
                {
                    ImageCropId = imageCrops[i].Id,
                    X = coordinateImageCrop.X,
                    Y = coordinateImageCrop.Y,
                    Width = coordinateImageCrop.Width,
                    Height = coordinateImageCrop.Height,
                    RowIndex = i,
                    OcrResults = ocrResults.OrderBy(x => x.x).ToList(),
                    BoundingSegment = rowSegments
                };
                cropRowResults.Add(cropRowResult);
            }

            // Process all crop rows at once to build boundingSegmentOcr
            var boundingSegmentOcr = new List<BoundingSegmentOcrDto>();
            foreach (var crop in cropRowResults)
            {
                // Create a spatial index for OCR results to speed up matching
                var ocrByXCoord = crop.OcrResults.ToList();

                foreach (var segment in crop.BoundingSegment)
                {
                    // More efficient matching using a single pass filter
                    var matched = ocrByXCoord
                        .Where(ocr => ocr.x >= segment.X && ocr.x <= (segment.X + segment.Width))
                        .ToList();

                    boundingSegmentOcr.Add(new BoundingSegmentOcrDto
                    {
                        X = segment.X,
                        Y = segment.Y,
                        Width = segment.Width,
                        Height = segment.Height,
                        RowIndex = crop.RowIndex,
                        OcrResults = matched
                    });
                }
            }

            // Sort once before adding to the main collection
            boundingSegmentOcrs.AddRange(boundingSegmentOcr.OrderBy(x => x.RowIndex).ThenBy(x => x.X));
        }


        // Process filterBoundingSegmentOcrs in one pass
        var filterBoundingSegmentOcrs = boundingSegmentOcrs
            .Select(x => new BoundingSegmentOcrDto
            {
                X = 0, // x.X - x.X is always 0
                Y = x.Y,
                Width = x.Width,
                Height = x.Height,
                RowIndex = x.RowIndex,
                OcrResults = x.OcrResults.Select(o => new OcrResultV2Dto
                {
                    id = o.id,
                    type = o.type,
                    x = o.x - x.X,
                    originalX = o.originalX,
                    y = o.y,
                    width = o.width,
                    height = o.height,
                    text = o.text,
                    rowIndex = o.rowIndex
                }).ToList()
            })
            .ToList();

        var alignBoundingSegmentOcrs = CalculationDepthHandler.AlignBoundingSegmentOcrs(filterBoundingSegmentOcrs);

        // Combine these operations to reduce iterations
        var horizontalOcrBlocks = alignBoundingSegmentOcrs
            .SelectMany(row => row.OcrResults)
            .Where(item => double.TryParse(item.text, NumberStyles.Any, CultureInfo.InvariantCulture, out _))
            .Select(item => new OcrBlockDto
            {
                X = item.x,
                OriginalX = item.originalX,
                Y = item.y,
                Width = item.width,
                Height = item.height,
                Value = double.Parse(item.text, CultureInfo.InvariantCulture),
                Type = item.type
            })
            .OrderBy(x => x.Value)
            .ToList();

        var nextBlock = horizontalOcrBlocks.FirstOrDefault(x => x.Value > input.DepthFrom)?.Value;
        if (nextBlock == null && input.DepthFrom < depthToMax)
        {
            nextBlock = depthToMax;
        }

        return nextBlock;
    }

    private static List<SegmentResultDto> ProcessOcrItems(List<OcrResultV2Dto> ocrItems, List<SegmentResultDto> rows)
    {
        var outputRows = new List<SegmentResultDto>();

        foreach (var inputRow in rows)
        {
            var rowIndex = inputRow.rowIndex;
            var bounding = inputRow.BoundingSegment;
            var startX = bounding.X;
            var endX = bounding.X + bounding.Width;

            var relevantOcrItems = ocrItems
                .Where(o => o.rowIndex == rowIndex && o.x >= startX && o.x <= endX)
                .ToList();

            var splitPoints = relevantOcrItems
                .Select(o => o.x)
                .Distinct()
                .OrderBy(x => x)
                .ToList();

            var points = new List<double> { startX };
            points.AddRange(splitPoints);
            if (!points.Contains(endX))
            {
                points.Add(endX);
            }

            for (var i = 0; i < points.Count - 1; i++)
            {
                var segStart = points[i];
                var segEnd = points[i + 1];
                if (segEnd > segStart)
                {
                    var newBounding = new CoordinateV2Dto
                    {
                        X = segStart,
                        Y = bounding.Y,
                        Width = segEnd - segStart,
                        Height = bounding.Height
                    };
                    var outputRow = new SegmentResultDto
                    {
                        Class = inputRow.Class,
                        rowIndex = rowIndex,
                        BoundingSegment = newBounding
                    };
                    outputRows.Add(outputRow);
                }
            }
        }

        return outputRows;
    }

    private static double InterpolateX(double startDepth, double startX, double endDepth, double endX, double depth,
        bool checkFirst, bool checkLast)
    {
        if (depth > endDepth && !checkLast)
        {
            return endX;
        }

        if (depth < startDepth && !checkFirst)
        {
            return startX;
        }

        return startX + (depth - startDepth) * (endX - startX) / (endDepth - startDepth);
    }
}